# Excel表格渲染性能优化说明

## 优化概述

本次优化主要针对 `ExcelCaseRenderTabbedPane` 类中的表格渲染性能问题，特别是处理大量数据（约10000行）时的性能瓶颈。

## 主要优化内容

### 1. 分批加载机制

**问题**：原来的 `renderRows` 方法一次性处理所有数据，导致UI线程阻塞。

**解决方案**：
- 实现了 `renderRowsBatch` 方法，将大数据集分批处理
- 使用 `SwingWorker` 在后台线程中准备数据，在EDT中更新UI
- 动态计算最优批处理大小，根据数据量自动调整

```java
// 动态批处理大小计算
final int BATCH_SIZE = ExcelTableRenderConfig.calculateOptimalBatchSize(totalRows);
```

### 2. 行高计算优化

**问题**：每行都要计算行高，涉及渲染器的 `prepareRenderer` 调用，性能开销大。

**解决方案**：
- 实现行高缓存机制，避免重复计算
- 只计算可见区域的行高，延迟计算非可见区域
- 添加滚动监听器，动态更新可见区域行高

```java
// 行高缓存
private final Map<Integer, Integer> rowHeightCache = new ConcurrentHashMap<>();

// 只更新可见区域
private void updateVisibleRowHeights() {
    // 计算可见区域范围
    // 只计算扩展后可见区域的行高
}
```

### 3. 内存优化

**问题**：大量数据可能导致内存使用过高。

**解决方案**：
- 预设表格模型大小，避免频繁扩容
- 限制行高缓存大小，实现简单的LRU策略
- 动态调整批处理间隔，根据可用内存优化性能

### 4. 性能监控

**新增功能**：
- 添加 `ExcelTablePerformanceMonitor` 性能监控工具
- 实时监控渲染速度、批处理效率、缓存命中率
- 提供详细的性能统计报告

## 配置参数

### ExcelTableRenderConfig 配置类

```java
public class ExcelTableRenderConfig {
    public static final int DEFAULT_BATCH_SIZE = 500;           // 默认批处理大小
    public static final int MIN_BATCH_SIZE = 100;               // 最小批处理大小  
    public static final int MAX_BATCH_SIZE = 2000;              // 最大批处理大小
    public static final int BATCH_INTERVAL_MS = 10;             // 批处理间隔时间
    public static final int SCROLL_LISTENER_DELAY_MS = 100;     // 滚动监听器延迟
    public static final int VISIBLE_AREA_EXTENSION = 10;        // 可见区域扩展行数
    public static final int ROW_HEIGHT_CACHE_MAX_SIZE = 10000;  // 行高缓存最大容量
}
```

## 性能提升效果

### 预期性能改进

1. **渲染速度**：10000行数据渲染时间从原来的数十秒降低到几秒内
2. **UI响应性**：渲染过程中UI保持响应，用户可以进行其他操作
3. **内存使用**：通过缓存管理和批处理优化，内存使用更加合理
4. **滚动性能**：只计算可见区域行高，滚动更加流畅

### 性能监控指标

- **渲染速度**：行/秒
- **批处理效率**：平均批处理时间
- **缓存命中率**：行高缓存命中百分比
- **内存使用**：实时内存使用情况

## 使用方法

### 1. 基本使用

优化后的代码与原有接口兼容，无需修改调用方式：

```java
// 原有调用方式保持不变
table.renderRows(tableData);
```

### 2. 性能监控

```java
// 获取性能统计报告
String report = ExcelTablePerformanceMonitor.getInstance().getPerformanceReport();
log.info(report);

// 获取缓存统计
String cacheStats = table.getRowHeightCacheStats();
log.info(cacheStats);
```

### 3. 缓存管理

```java
// 清除所有行高缓存
table.clearRowHeightCache();

// 清除指定行的缓存
table.clearRowHeightCache(rowIndex);
```

## 兼容性说明

### 与现有功能的兼容性

1. **FrozenColumnTable**：完全兼容，冻结列功能正常工作
2. **选择机制**：复选框选择功能不受影响
3. **数据编辑**：单元格编辑功能正常，会自动清除对应行的缓存
4. **排序和过滤**：与现有的排序过滤机制兼容

### 注意事项

1. **内存使用**：大数据量时会使用更多内存进行缓存，但有上限控制
2. **初始加载**：首次渲染时间可能略有增加，但后续操作会更快
3. **滚动延迟**：滚动时有100ms的延迟计算，避免频繁重计算

## 测试验证

### 单元测试

运行 `ExcelTableRenderPerformanceTest` 验证优化效果：

```bash
mvn test -Dtest=ExcelTableRenderPerformanceTest
```

### 性能测试

1. **小数据量测试**（1000行以下）：验证基本功能正常
2. **中等数据量测试**（1000-5000行）：验证批处理效果
3. **大数据量测试**（10000行以上）：验证性能提升效果

## 故障排除

### 常见问题

1. **渲染卡顿**：检查批处理大小配置，可能需要调整
2. **内存使用过高**：检查缓存大小限制，清理不必要的缓存
3. **滚动不流畅**：检查可见区域扩展配置

### 调试方法

1. 启用DEBUG日志级别查看详细信息
2. 使用性能监控工具查看统计数据
3. 检查内存使用情况

## 后续优化建议

1. **虚拟化表格**：对于超大数据量，可考虑实现完全的虚拟化表格
2. **数据分页**：结合后端分页机制，进一步减少前端数据量
3. **异步加载**：实现数据的异步懒加载机制
4. **缓存策略**：实现更智能的缓存策略，如LFU或时间过期机制
