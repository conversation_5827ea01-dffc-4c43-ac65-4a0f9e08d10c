package ui.performance;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * Test for 100% accurate selection count mechanism
 */
public class AccurateSelectionCountTest {
    
    public static void main(String[] args) {
        System.out.println("=== 100% Accurate Selection Count Test ===");
        
        // Test counter mechanism
        testCounterMechanism();
        
        // Test incremental updates
        testIncrementalUpdates();
        
        // Test async initialization
        testAsyncInitialization();
        
        // Test performance with large dataset
        testLargeDatasetPerformance();
        
        System.out.println("=== All accurate selection tests completed ===");
    }
    
    private static void testCounterMechanism() {
        System.out.println("\n--- Testing Counter Mechanism ---");
        
        MockSelectionCounter counter = new MockSelectionCounter();
        
        // Test empty dataset
        boolean result = counter.isAllSelected(createDataset(0, false));
        System.out.printf("Empty dataset all-selected: %s (expected: false)%n", result);
        
        // Test small all-selected dataset
        result = counter.isAllSelected(createDataset(100, true));
        System.out.printf("Small all-selected dataset: %s (expected: true)%n", result);
        
        // Test small partially-selected dataset
        List<MockExcelRow> partialDataset = createDataset(100, true);
        partialDataset.get(50).setSelected(false);
        result = counter.isAllSelected(partialDataset);
        System.out.printf("Small partial-selected dataset: %s (expected: false)%n", result);
        
        // Test accuracy
        if (result == false) {
            System.out.println("Counter mechanism test: PASS");
        } else {
            System.out.println("Counter mechanism test: FAIL");
        }
    }
    
    private static void testIncrementalUpdates() {
        System.out.println("\n--- Testing Incremental Updates ---");
        
        MockSelectionCounter counter = new MockSelectionCounter();
        List<MockExcelRow> dataset = createDataset(1000, false);
        
        // Initialize counter
        counter.initializeCounter(dataset);
        System.out.printf("Initial state: %s%n", counter.getStats());
        
        // Select some rows incrementally
        for (int i = 0; i < 500; i++) {
            dataset.get(i).setSelected(true);
            counter.updateCount(i, false, true);
        }
        System.out.printf("After selecting 500 rows: %s%n", counter.getStats());
        
        // Select remaining rows
        for (int i = 500; i < 1000; i++) {
            dataset.get(i).setSelected(true);
            counter.updateCount(i, false, true);
        }
        System.out.printf("After selecting all rows: %s%n", counter.getStats());
        
        // Verify all-selected state
        boolean allSelected = counter.isAllSelectedByCounter();
        System.out.printf("All-selected by counter: %s (expected: true)%n", allSelected);
        
        // Deselect one row
        dataset.get(999).setSelected(false);
        counter.updateCount(999, true, false);
        System.out.printf("After deselecting one row: %s%n", counter.getStats());
        
        allSelected = counter.isAllSelectedByCounter();
        System.out.printf("All-selected after deselection: %s (expected: false)%n", allSelected);
        
        if (!allSelected) {
            System.out.println("Incremental updates test: PASS");
        } else {
            System.out.println("Incremental updates test: FAIL");
        }
    }
    
    private static void testAsyncInitialization() {
        System.out.println("\n--- Testing Async Initialization ---");
        
        MockSelectionCounter counter = new MockSelectionCounter();
        List<MockExcelRow> largeDataset = createDataset(10000, true);
        
        // Set some rows to unselected
        largeDataset.get(5000).setSelected(false);
        largeDataset.get(8000).setSelected(false);
        
        CountDownLatch latch = new CountDownLatch(1);
        
        long startTime = System.currentTimeMillis();
        
        // Simulate async initialization
        new Thread(() -> {
            try {
                counter.initializeCounterAsync(largeDataset);
                latch.countDown();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }).start();
        
        // UI should not be blocked
        long uiResponseTime = System.currentTimeMillis() - startTime;
        System.out.printf("UI response time: %d ms (should be < 10ms)%n", uiResponseTime);
        
        try {
            boolean completed = latch.await(5, TimeUnit.SECONDS);
            if (completed) {
                System.out.printf("Async initialization completed: %s%n", counter.getStats());
                boolean allSelected = counter.isAllSelectedByCounter();
                System.out.printf("All-selected result: %s (expected: false)%n", allSelected);
                
                if (!allSelected && uiResponseTime < 50) {
                    System.out.println("Async initialization test: PASS");
                } else {
                    System.out.println("Async initialization test: FAIL");
                }
            } else {
                System.out.println("Async initialization test: TIMEOUT");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    private static void testLargeDatasetPerformance() {
        System.out.println("\n--- Testing Large Dataset Performance ---");
        
        MockSelectionCounter counter = new MockSelectionCounter();
        
        // Test with 12670 rows (same as real scenario)
        List<MockExcelRow> largeDataset = createDataset(12670, true);
        
        // Sync initialization for small threshold
        long startTime = System.currentTimeMillis();
        counter.initializeCounter(createDataset(1000, true));
        long syncDuration = System.currentTimeMillis() - startTime;
        System.out.printf("Sync initialization (1000 rows): %d ms%n", syncDuration);
        
        // Async initialization for large dataset
        startTime = System.currentTimeMillis();
        counter.initializeCounterAsync(largeDataset);
        long asyncStartTime = System.currentTimeMillis() - startTime;
        System.out.printf("Async initialization start time (12670 rows): %d ms%n", asyncStartTime);
        
        // Test incremental update performance
        startTime = System.currentTimeMillis();
        for (int i = 0; i < 100; i++) {
            counter.updateCount(i, true, false);
        }
        long incrementalDuration = System.currentTimeMillis() - startTime;
        System.out.printf("100 incremental updates: %d ms%n", incrementalDuration);
        
        // Test counter query performance
        startTime = System.currentTimeMillis();
        for (int i = 0; i < 1000; i++) {
            counter.isAllSelectedByCounter();
        }
        long queryDuration = System.currentTimeMillis() - startTime;
        System.out.printf("1000 counter queries: %d ms%n", queryDuration);
        
        if (syncDuration < 100 && asyncStartTime < 10 && 
            incrementalDuration < 10 && queryDuration < 10) {
            System.out.println("Large dataset performance test: PASS");
        } else {
            System.out.println("Large dataset performance test: FAIL");
        }
    }
    
    /**
     * Create test dataset
     */
    private static List<MockExcelRow> createDataset(int size, boolean allSelected) {
        List<MockExcelRow> dataset = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            MockExcelRow row = new MockExcelRow();
            row.setSelected(allSelected);
            dataset.add(row);
        }
        return dataset;
    }
    
    /**
     * Mock selection counter
     */
    private static class MockSelectionCounter {
        private volatile int selectedCount = 0;
        private volatile int totalCount = 0;
        private volatile boolean isValid = false;
        private final Object lock = new Object();
        
        public boolean isAllSelected(List<MockExcelRow> dataset) {
            if (dataset.isEmpty()) {
                return false;
            }
            
            // For small datasets, calculate directly
            if (dataset.size() <= 1000) {
                return calculateAndUpdate(dataset, true);
            }
            
            // For large datasets, use async initialization
            if (!isValid) {
                initializeCounterAsync(dataset);
                return false; // Conservative return
            }
            
            return isAllSelectedByCounter();
        }
        
        public boolean isAllSelectedByCounter() {
            synchronized (lock) {
                return isValid && totalCount > 0 && selectedCount == totalCount;
            }
        }
        
        public void initializeCounter(List<MockExcelRow> dataset) {
            calculateAndUpdate(dataset, true);
        }
        
        public void initializeCounterAsync(List<MockExcelRow> dataset) {
            new Thread(() -> calculateAndUpdate(dataset, false)).start();
        }
        
        private boolean calculateAndUpdate(List<MockExcelRow> dataset, boolean returnResult) {
            int selected = 0;
            int total = dataset.size();
            
            for (MockExcelRow row : dataset) {
                if (row.isSelected()) {
                    selected++;
                }
                
                // Simulate processing time for large datasets
                if (total > 5000 && selected % 1000 == 0) {
                    try {
                        Thread.sleep(1);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
            
            synchronized (lock) {
                this.selectedCount = selected;
                this.totalCount = total;
                this.isValid = true;
            }
            
            return returnResult && (selected == total && total > 0);
        }
        
        public void updateCount(int row, boolean wasSelected, boolean isSelected) {
            synchronized (lock) {
                if (!isValid) return;
                
                if (wasSelected && !isSelected) {
                    selectedCount--;
                } else if (!wasSelected && isSelected) {
                    selectedCount++;
                }
                
                selectedCount = Math.max(0, Math.min(selectedCount, totalCount));
            }
        }
        
        public String getStats() {
            synchronized (lock) {
                return String.format("Selected: %d/%d, Valid: %s, All-selected: %s",
                        selectedCount, totalCount, isValid, 
                        isValid && totalCount > 0 && selectedCount == totalCount);
            }
        }
    }
    
    /**
     * Mock Excel row for testing
     */
    private static class MockExcelRow {
        private boolean selected;
        
        public boolean isSelected() {
            return selected;
        }
        
        public void setSelected(boolean selected) {
            this.selected = selected;
        }
    }
}
