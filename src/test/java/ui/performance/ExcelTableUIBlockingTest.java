package ui.performance;

import ui.layout.left.display.components.tappane.case_mgmt.excelcase.ExcelTableRenderConfig;
import ui.layout.left.display.components.tappane.case_mgmt.excelcase.ExcelTableThreadManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * Test for Excel table UI blocking issues
 */
public class ExcelTableUIBlockingTest {
    
    public static void main(String[] args) {
        System.out.println("=== Excel Table UI Blocking Fix Test ===");
        
        // Test async processing
        testAsyncProcessing();
        
        // Test optimized all-selected check
        testOptimizedAllSelectedCheck();
        
        // Test thread management
        testThreadManagement();
        
        // Test visible row height calculation
        testVisibleRowHeightCalculation();
        
        System.out.println("=== All UI blocking tests completed ===");
        
        // Cleanup
        ExcelTableThreadManager.getInstance().shutdown();
    }
    
    private static void testAsyncProcessing() {
        System.out.println("\n--- Testing Async Processing ---");
        
        long startTime = System.currentTimeMillis();
        CountDownLatch latch = new CountDownLatch(1);
        
        // Simulate async afterDataLoaded processing
        CompletableFuture.runAsync(() -> {
            try {
                // Simulate time-consuming operation
                Thread.sleep(100);
                System.out.println("Async afterDataLoaded processing completed");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                latch.countDown();
            }
        }, ExcelTableThreadManager.getInstance().getBackgroundExecutor());
        
        // UI should not be blocked
        long uiResponseTime = System.currentTimeMillis() - startTime;
        System.out.printf("UI response time: %d ms (should be < 10ms)%n", uiResponseTime);
        
        try {
            boolean completed = latch.await(5, TimeUnit.SECONDS);
            System.out.printf("Async processing completed: %s%n", completed ? "SUCCESS" : "TIMEOUT");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        if (uiResponseTime < 10) {
            System.out.println("Async processing test: PASS");
        } else {
            System.out.println("Async processing test: FAIL");
        }
    }
    
    private static void testOptimizedAllSelectedCheck() {
        System.out.println("\n--- Testing Optimized All-Selected Check ---");
        
        // Create large dataset
        List<MockExcelRow> largeDataset = new ArrayList<>();
        for (int i = 0; i < 12670; i++) {
            MockExcelRow row = new MockExcelRow();
            row.setSelected(true); // All selected
            largeDataset.add(row);
        }
        
        // Test sampling-based check
        long startTime = System.currentTimeMillis();
        boolean allSelected = isAllSelectedOptimized(largeDataset);
        long duration = System.currentTimeMillis() - startTime;
        
        System.out.printf("Optimized all-selected check for %d rows: %s in %d ms%n", 
                largeDataset.size(), allSelected, duration);
        
        // Test with some unselected rows
        largeDataset.get(50).setSelected(false);
        largeDataset.get(6000).setSelected(false);
        
        startTime = System.currentTimeMillis();
        allSelected = isAllSelectedOptimized(largeDataset);
        duration = System.currentTimeMillis() - startTime;
        
        System.out.printf("Optimized check with unselected rows: %s in %d ms%n", allSelected, duration);
        
        if (duration < 50) {
            System.out.println("Optimized all-selected check test: PASS");
        } else {
            System.out.println("Optimized all-selected check test: FAIL");
        }
    }
    
    private static void testThreadManagement() {
        System.out.println("\n--- Testing Thread Management ---");
        
        ExcelTableThreadManager manager = ExcelTableThreadManager.getInstance();
        System.out.printf("Thread manager status: %s%n", manager.getStatus());
        
        // Test multiple async operations
        CountDownLatch latch = new CountDownLatch(3);
        
        for (int i = 0; i < 3; i++) {
            final int taskId = i;
            CompletableFuture.runAsync(() -> {
                try {
                    Thread.sleep(50);
                    System.out.printf("Background task %d completed%n", taskId);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    latch.countDown();
                }
            }, manager.getBackgroundExecutor());
        }
        
        try {
            boolean completed = latch.await(5, TimeUnit.SECONDS);
            System.out.printf("All background tasks completed: %s%n", completed ? "SUCCESS" : "TIMEOUT");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        System.out.println("Thread management test: PASS");
    }
    
    private static void testVisibleRowHeightCalculation() {
        System.out.println("\n--- Testing Visible Row Height Calculation ---");
        
        // Simulate visible area calculation
        int totalRows = 12670;
        int visibleRows = 20;
        int extension = ExcelTableRenderConfig.VISIBLE_AREA_EXTENSION;
        int maxCalculate = 50;
        
        int startRow = Math.max(0, 0 - extension);
        int endRow = Math.min(totalRows - 1, visibleRows + extension);
        
        if (endRow - startRow + 1 > maxCalculate) {
            endRow = startRow + maxCalculate - 1;
        }
        
        int actualCalculated = endRow - startRow + 1;
        
        System.out.printf("Total rows: %d, Visible: %d, Extension: %d%n", totalRows, visibleRows, extension);
        System.out.printf("Calculated rows: %d (limited to %d)%n", actualCalculated, maxCalculate);
        
        // Simulate row height calculation time
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < actualCalculated; i++) {
            // Simulate row height calculation (1ms per row)
            try {
                Thread.sleep(1);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        long duration = System.currentTimeMillis() - startTime;
        
        System.out.printf("Row height calculation time: %d ms%n", duration);
        
        if (duration < 100 && actualCalculated <= maxCalculate) {
            System.out.println("Visible row height calculation test: PASS");
        } else {
            System.out.println("Visible row height calculation test: FAIL");
        }
    }
    
    /**
     * Simulate optimized all-selected check
     */
    private static boolean isAllSelectedOptimized(List<MockExcelRow> tableList) {
        if (tableList.isEmpty()) {
            return false;
        }
        
        // For large datasets, use sampling instead of full check
        if (tableList.size() > 5000) {
            int sampleSize = Math.min(100, tableList.size() / 10);
            
            // Check front
            for (int i = 0; i < sampleSize && i < tableList.size(); i++) {
                if (!tableList.get(i).isSelected()) {
                    return false;
                }
            }
            
            // Check middle
            int midStart = tableList.size() / 2 - sampleSize / 2;
            for (int i = midStart; i < midStart + sampleSize && i < tableList.size(); i++) {
                if (!tableList.get(i).isSelected()) {
                    return false;
                }
            }
            
            // Check end
            int endStart = Math.max(0, tableList.size() - sampleSize);
            for (int i = endStart; i < tableList.size(); i++) {
                if (!tableList.get(i).isSelected()) {
                    return false;
                }
            }
            
            return true;
        } else {
            // Small dataset - full check
            return tableList.stream().allMatch(MockExcelRow::isSelected);
        }
    }
    
    /**
     * Mock Excel row for testing
     */
    private static class MockExcelRow {
        private boolean selected;
        
        public boolean isSelected() {
            return selected;
        }
        
        public void setSelected(boolean selected) {
            this.selected = selected;
        }
    }
}
