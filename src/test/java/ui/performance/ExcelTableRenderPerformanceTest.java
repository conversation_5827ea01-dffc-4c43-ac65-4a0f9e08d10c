package ui.performance;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import ui.layout.left.display.components.tappane.case_mgmt.excelcase.ExcelTableRenderConfig;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Excel表格渲染性能测试
 */
public class ExcelTableRenderPerformanceTest {
    
    private List<HashMap<Integer, String>> testData;
    
    @BeforeEach
    void setUp() {
        // 创建测试数据
        testData = createTestData(10000); // 10000行测试数据
    }
    
    @Test
    @DisplayName("测试批处理大小计算")
    void testBatchSizeCalculation() {
        // 测试不同数据量的批处理大小计算
        assertEquals(100, ExcelTableRenderConfig.calculateOptimalBatchSize(500));
        assertEquals(100, ExcelTableRenderConfig.calculateOptimalBatchSize(1000));
        assertEquals(500, ExcelTableRenderConfig.calculateOptimalBatchSize(3000));
        assertEquals(1000, ExcelTableRenderConfig.calculateOptimalBatchSize(10000));
        assertEquals(2000, ExcelTableRenderConfig.calculateOptimalBatchSize(50000));
    }
    
    @Test
    @DisplayName("测试批处理间隔计算")
    void testBatchIntervalCalculation() {
        // 测试不同内存情况下的间隔计算
        assertEquals(5, ExcelTableRenderConfig.calculateBatchInterval(2048)); // 2GB
        assertEquals(10, ExcelTableRenderConfig.calculateBatchInterval(1024)); // 1GB
        assertEquals(10, ExcelTableRenderConfig.calculateBatchInterval(800)); // 800MB
        assertEquals(20, ExcelTableRenderConfig.calculateBatchInterval(256)); // 256MB
    }
    
    @Test
    @DisplayName("测试分批处理性能")
    void testBatchProcessingPerformance() {
        int totalRows = testData.size();
        int batchSize = ExcelTableRenderConfig.calculateOptimalBatchSize(totalRows);
        
        long startTime = System.nanoTime();
        
        // 模拟分批处理
        for (int startIndex = 0; startIndex < totalRows; startIndex += batchSize) {
            int endIndex = Math.min(startIndex + batchSize, totalRows);
            List<HashMap<Integer, String>> batch = testData.subList(startIndex, endIndex);
            
            // 模拟批处理逻辑
            processBatch(batch);
        }
        
        long endTime = System.nanoTime();
        long duration = TimeUnit.NANOSECONDS.toMillis(endTime - startTime);
        
        System.out.printf("分批处理%d行数据耗时: %d ms%n", totalRows, duration);
        
        // 验证性能要求：10000行数据应该在合理时间内完成
        assertTrue(duration < 5000, "分批处理耗时过长: " + duration + "ms");
    }
    
    @Test
    @DisplayName("测试内存使用优化")
    void testMemoryOptimization() {
        Runtime runtime = Runtime.getRuntime();
        
        // 记录初始内存使用
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 模拟大量数据处理
        List<Object[]> processedData = new ArrayList<>();
        for (HashMap<Integer, String> rowData : testData) {
            Object[] converted = convertRowData(rowData);
            processedData.add(converted);
        }
        
        // 记录处理后内存使用
        long afterProcessMemory = runtime.totalMemory() - runtime.freeMemory();
        long memoryIncrease = afterProcessMemory - initialMemory;
        
        System.out.printf("处理%d行数据内存增长: %d bytes (%.2f MB)%n", 
                testData.size(), memoryIncrease, memoryIncrease / (1024.0 * 1024.0));
        
        // 验证内存使用合理性
        assertTrue(memoryIncrease < 100 * 1024 * 1024, "内存使用过多: " + memoryIncrease + " bytes");
    }
    
    @Test
    @DisplayName("测试行高缓存效果")
    void testRowHeightCaching() {
        Map<Integer, Integer> cache = new HashMap<>();
        
        // 模拟行高计算和缓存
        long startTime = System.nanoTime();
        
        for (int i = 0; i < 1000; i++) {
            int row = i % 100; // 重复访问前100行
            
            if (cache.containsKey(row)) {
                // 缓存命中
                int height = cache.get(row);
            } else {
                // 计算行高（模拟耗时操作）
                int height = calculateRowHeight(row);
                cache.put(row, height);
            }
        }
        
        long endTime = System.nanoTime();
        long duration = TimeUnit.NANOSECONDS.toMillis(endTime - startTime);
        
        System.out.printf("行高缓存测试耗时: %d ms, 缓存大小: %d%n", duration, cache.size());
        
        // 验证缓存效果
        assertEquals(100, cache.size(), "缓存大小不正确");
        assertTrue(duration < 100, "缓存效果不明显: " + duration + "ms");
    }
    
    /**
     * 创建测试数据
     */
    private List<HashMap<Integer, String>> createTestData(int rowCount) {
        List<HashMap<Integer, String>> data = new ArrayList<>();
        
        for (int i = 0; i < rowCount; i++) {
            HashMap<Integer, String> row = new HashMap<>();
            row.put(0, "Row " + i);
            row.put(1, "Test Case " + i);
            row.put(2, "Description for test case " + i + " with some longer text to simulate real data");
            row.put(3, "Expected result " + i);
            row.put(4, i % 2 == 0 ? "YES" : "NO");
            data.add(row);
        }
        
        return data;
    }
    
    /**
     * 模拟批处理逻辑
     */
    private void processBatch(List<HashMap<Integer, String>> batch) {
        for (HashMap<Integer, String> row : batch) {
            // 模拟数据转换
            convertRowData(row);
        }
    }
    
    /**
     * 模拟行数据转换
     */
    private Object[] convertRowData(HashMap<Integer, String> rowData) {
        Object[] result = new Object[rowData.size() + 1];
        result[0] = false; // 选择列
        
        for (int i = 0; i < rowData.size(); i++) {
            result[i + 1] = rowData.get(i);
        }
        
        return result;
    }
    
    /**
     * 模拟行高计算
     */
    private int calculateRowHeight(int row) {
        // 模拟计算耗时
        try {
            Thread.sleep(1);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 返回模拟的行高
        return 20 + (row % 5) * 5; // 20-40像素的行高
    }
}
