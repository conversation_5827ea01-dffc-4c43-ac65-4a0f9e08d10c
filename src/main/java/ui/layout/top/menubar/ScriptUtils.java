package ui.layout.top.menubar;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import common.constant.AppConstants;
import common.exceptions.OperationException;
import common.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import sdk.base.JsonResponse;
import sdk.constants.UrlConstants;
import sdk.domain.Device;
import sdk.domain.TestScriptFile;
import sdk.domain.TestScriptFileContent;
import sdk.domain.complex.PercentTemplateRoi;
import sdk.domain.image.TemplateRoiQuery;
import sdk.domain.robot.RobotCoordinates;
import sdk.domain.robot.RobotCoordinatesQuery;
import sdk.domain.screen.ScreenConfig;
import sdk.entity.OperationTargetHolder;
import ui.layout.left.display.components.tappane.case_mgmt.scriptcase.TestScriptCaseDisplayTable;
import ui.layout.left.display.components.tappane.case_mgmt.scriptcase.testcasepackage.ExportFileData;
import ui.layout.left.display.components.tappane.case_mgmt.scriptcase.testcasepackage.JsonFileCreator;
import ui.layout.left.display.components.tappane.case_mgmt.scriptcase.testcasepackage.JsonFileParser;
import ui.model.MainModel;

import javax.swing.*;
import javax.swing.filechooser.FileNameExtensionFilter;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.*;

import static sdk.base.BaseHttpClient.defaultPostJsonResponse;

@Slf4j
public class ScriptUtils {
    private static volatile ScriptUtils scriptUtils;
    private final MainModel mainModel;
    private final TestScriptCaseDisplayTable testScriptCaseDisplayTable;
    private final static String PROJECT_PATH = "D:\\FlyTest\\data\\server\\projects";
    private final static String DEVICE_CONFIG_PATH = "D:\\FlyTest\\data\\client\\app\\config\\devicesConfig.json";
    private final static String NO_SCRIPT_WARNING = "请至少勾选一个测试脚本";

    private ScriptUtils(MainModel mainModel, TestScriptCaseDisplayTable testScriptCaseDisplayTable) {
        this.mainModel = mainModel;
        this.testScriptCaseDisplayTable = testScriptCaseDisplayTable;
    }

    public static ScriptUtils getInstance(MainModel mainModel, TestScriptCaseDisplayTable testScriptCaseDisplayTable) {
        if (scriptUtils == null) {
            synchronized (ScriptUtils.class) {
                if (scriptUtils == null) {
                    scriptUtils = new ScriptUtils(mainModel, testScriptCaseDisplayTable);
                }
            }
        }
        return scriptUtils;
    }

    public void importFileScript() {
        String projectName = mainModel.getAppInfo().getProject();
        String jsonFilePath = String.format("%s\\%s\\database\\fileDB\\testcases", PROJECT_PATH, projectName);
        JFileChooser fileChooser = new JFileChooser(new File(jsonFilePath));
        FileNameExtensionFilter filter = new FileNameExtensionFilter("用例文件 (*.json)", "json");
        fileChooser.setFileFilter(filter);
        fileChooser.setMultiSelectionEnabled(true);
        int result = fileChooser.showOpenDialog(null);
        if (result == JFileChooser.APPROVE_OPTION) {
            File[] selectedFiles = fileChooser.getSelectedFiles();
            for (File selectedFile : selectedFiles) {
                String selectedFilePath = selectedFile.getAbsolutePath();
                JsonFileParser parser = new JsonFileParser(selectedFilePath, jsonFilePath);
                parser.parseJsonFile();
                File[] scriptJson = parser.getJsonFiles();
                if (scriptJson.length == 0) {
                    JOptionPane.showMessageDialog(null, "脚本格式错误");
                    continue;
                }
                for (File testcaseFile : scriptJson) {
                    String fileName = testcaseFile.getName();
                    String jsonStr;
                    try {
                        jsonStr = FileUtils.readStringFromFile(testcaseFile);
                    } catch (IOException e) {
                        JOptionPane.showMessageDialog(null, String.format("脚本读取出错, 文件:%s, 原因:%s", fileName, e.getMessage()));
                        break;
                    }
                    TestScriptFileContent testScriptFileContent = JSON.parseObject(jsonStr, TestScriptFileContent.class);
                    if (testScriptFileContent == null) {
                        JOptionPane.showMessageDialog(null, String.format("脚本数据格式出错,文件:%s", fileName));
                        break;
                    }
                    TestScriptFile importTestScriptFile = createTestScriptFile(fileName, testcaseFile);
                    try {
                        mainModel.getTestScriptEventModel().addScript(importTestScriptFile);
                    } catch (OperationException e) {
                        JOptionPane.showMessageDialog(null, String.format("文件:%s, %s", fileName, e.getMessage()));
                        testScriptCaseDisplayTable.selectRowAndSwitchScript();
                    }
                }
                //importScreenConfig(parser, projectName);
                //importRobotCoordinates(parser, projectName, projectId);
            }
        }
    }

    private TestScriptFile createTestScriptFile(String fileName, File testcaseFile) {
        TestScriptFile importTestScriptFile = new TestScriptFile();
        importTestScriptFile.setProjectName(mainModel.getAppInfo().getProject());
        importTestScriptFile.setCaseName(fileName.replace(".json", ""));
        importTestScriptFile.setClientName(AppConstants.APP_NAME);
        importTestScriptFile.setImported(true);
        importTestScriptFile.setImportedFilePath(testcaseFile.getAbsolutePath());
        return importTestScriptFile;
    }

    private void importScreenConfig(JsonFileParser parser, String projectName) {
        ScreenConfig screenConfig = parser.getScreenConfig();
        screenConfig.setProject(projectName);
        OperationTargetHolder.getScreenKit().updateConfig(screenConfig);
    }

    private void importRobotCoordinates(JsonFileParser parser, String projectName, Integer projectId) {
        Optional<Integer> robotDeviceId = getRobotIdAliasAndProject("Dobot_MG400#1");
        if (robotDeviceId.isPresent()) {
            Map<String, List<RobotCoordinates>> deviceRobotCoordinatesMap = parser.getDeviceRobotCoordinatesMap();
            for (String deviceUniqueCode : deviceRobotCoordinatesMap.keySet()) {
                List<RobotCoordinates> robotCoordinatesList = deviceRobotCoordinatesMap.get(deviceUniqueCode);
                for (RobotCoordinates robotCoordinates : robotCoordinatesList) {
                    robotCoordinates.setId(null);
                    robotCoordinates.setUuid(null);
                    robotCoordinates.setProjectName(projectName);
                    robotCoordinates.setProjectId(null);
                    robotCoordinates.setDeviceUniqueCode(deviceUniqueCode);
                    robotCoordinates.setDeviceId(robotDeviceId.get());
                    defaultPostJsonResponse(UrlConstants.RobotCoordinatesUrls.ADD_ROBOT_COORDINATES,
                            robotCoordinates, new TypeReference<JsonResponse<RobotCoordinates>>() {
                            });
                }
            }
        }
    }

    public void exportFileScript() {
        List<Integer> checkedRows = testScriptCaseDisplayTable.getCheckedRows();
        List<String> checkedRowsName = new ArrayList<>();
        if (!checkedRows.isEmpty()) {
            for (int i : checkedRows) {
                checkedRowsName.add(testScriptCaseDisplayTable.getTestScriptName(i));
            }
        } else {
            JOptionPane.showMessageDialog(null, NO_SCRIPT_WARNING);
            return;
        }
        String jsonFilePath = String.format("%s\\%s\\database\\fileDB\\testcases", PROJECT_PATH, mainModel.getAppInfo().getProject());
        String photoFilePath = String.format("%s\\%s\\database\\fileDB\\pictures\\camera", PROJECT_PATH, mainModel.getAppInfo().getProject());
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setCurrentDirectory(new File(System.getProperty("user.home")));
        fileChooser.setDialogTitle("选择文件保存位置和名称");
        fileChooser.setFileSelectionMode(JFileChooser.FILES_ONLY);
        FileNameExtensionFilter filter = new FileNameExtensionFilter("用例文件 (*.json)", "json");
        fileChooser.setFileFilter(filter);
        fileChooser.setAcceptAllFileFilterUsed(false);
        String filePath;
        int userSelection = fileChooser.showSaveDialog(null);
        if (userSelection == JFileChooser.APPROVE_OPTION) {
            File fileToSave = fileChooser.getSelectedFile();
            if (!fileToSave.getAbsolutePath().endsWith(".json")) {
                filePath = fileToSave.getAbsolutePath() + ".json";
            } else {
                filePath = fileToSave.getAbsolutePath();
            }
            File file = new File(filePath);
            if (file.exists()) {
                int response = JOptionPane.showConfirmDialog(null, "文件已存在,是否覆盖？", "文件已存在", JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);
                if (response == JOptionPane.NO_OPTION) {
                    filePath = "";
                }
            }
            List<String> jsonFilePaths = getSelectedJsonFiles(jsonFilePath, checkedRowsName);//json路径
            List<String> imageFilePaths = new ArrayList<>();
            findPngFiles(photoFilePath, imageFilePaths);//图片路径
            if (!filePath.isEmpty()) {
                ExportFileData exportFileData = ExportFileData.builder().filePath(filePath)
                        .jsonFilePaths(jsonFilePaths)
                        .imageFilePaths(imageFilePaths)
                        .deviceRobotCoordinatesMap(getRobotCoordinates())
                        .screenConfig(getScreenConfig())
                        .build();
                if (JsonFileCreator.createJsonFile(exportFileData)) {
                    JOptionPane.showMessageDialog(null, "文件导出成功");
                }
            }
        }
    }

    /**
     * @param directoryPath:
     * @param pngFiles:png文件路径
     * <AUTHOR>
     * @description 找所有图片
     * @date 2024/2/25 21:13
     */
    public static void findPngFiles(String directoryPath, List<String> pngFiles) {
        File directory = new File(directoryPath);
        File[] subdirectories = directory.listFiles(File::isDirectory);
        if (subdirectories == null) {
            return;
        }
        for (File subdirectory : subdirectories) {
            File templatePicturesDir = new File(subdirectory, "templatePictures");
            if (!templatePicturesDir.exists() || !templatePicturesDir.isDirectory()) {
                continue;
            }
            File[] files = templatePicturesDir.listFiles((dir, name) -> name.endsWith(".png"));
            if (files == null) {
                continue;
            }
            for (File file : files) {
                pngFiles.add(file.getAbsolutePath());
            }
        }
    }

    private void exportFileScriptNew() throws IOException {
        List<Integer> checkedRows = testScriptCaseDisplayTable.getCheckedRows();
        List<String> checkedRowsName = new ArrayList<>();
        if (!checkedRows.isEmpty()) {
            for (int i : checkedRows) {
                checkedRowsName.add(testScriptCaseDisplayTable.getTestScriptName(i));
            }
        } else {
            JOptionPane.showMessageDialog(null, NO_SCRIPT_WARNING);
            return;
        }
        //读取
        String jsonFilePath = String.format("%s\\%s\\database\\fileDB\\testcases", PROJECT_PATH, mainModel.getAppInfo().getProject());
        String photoFilePath = String.format("%s\\%s\\database\\fileDB\\pictures\\camera", PROJECT_PATH, mainModel.getAppInfo().getProject());
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setCurrentDirectory(new File(System.getProperty("user.home")));
        fileChooser.setDialogTitle("选择文件保存位置和名称");
        fileChooser.setFileSelectionMode(JFileChooser.FILES_ONLY);
        FileNameExtensionFilter filter = new FileNameExtensionFilter("用例文件 (*.json)", "json");
        fileChooser.setFileFilter(filter);
        fileChooser.setAcceptAllFileFilterUsed(false);
        String filePath;
        int userSelection = fileChooser.showSaveDialog(null);
        if (userSelection == JFileChooser.APPROVE_OPTION) {
            File fileToSave = fileChooser.getSelectedFile();
            if (!fileToSave.getAbsolutePath().endsWith(".json")) {
                filePath = fileToSave.getAbsolutePath() + ".json";
            } else {
                filePath = fileToSave.getAbsolutePath();
            }
            File file = new File(filePath);
            if (file.exists()) {
                int response = JOptionPane.showConfirmDialog(null, "文件已存在,是否覆盖？", "文件已存在", JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);
                if (response == JOptionPane.NO_OPTION) {
                    filePath = "";
                }
            }
            List<String> jsonFilePaths = getSelectedJsonFiles(jsonFilePath, checkedRowsName);//json路径
            List<String> imageFilePaths = new ArrayList<>();
            findPngFiles(photoFilePath, imageFilePaths);//图片路径
            if (!filePath.isEmpty()) {
                ExportFileData exportFileData = ExportFileData.builder().filePath(filePath)
                        .jsonFilePaths(jsonFilePaths)
                        .imageFilePaths(imageFilePaths)
                        .deviceRobotCoordinatesMap(getRobotCoordinates())
                        .screenConfig(getScreenConfig())
                        .build();
                if (JsonFileCreator.createJsonFile(exportFileData)) {
                    JOptionPane.showMessageDialog(null, "文件导出成功");
                }
            }
        }
    }

    public static List<String> findUniqueCodesByDeviceTypeAndProject(String deviceType, String projectName) {
        List<String> deviceUniqueCodes = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new FileReader(DEVICE_CONFIG_PATH))) {
            StringBuilder jsonString = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                jsonString.append(line);
            }
            JSONObject rootObject = JSONObject.from(JSON.parseObject(jsonString.toString()));
            if (rootObject == null) {
                return new ArrayList<>();
            }
            JSONArray devicesArray = rootObject.getJSONArray("devices");
            for (Object deviceItem : devicesArray) {
                JSONObject deviceObj = (JSONObject) deviceItem;
                String currentDeviceType = deviceObj.getString("deviceType");
                if (currentDeviceType.equals(deviceType)) {
                    JSONArray modelsArray = deviceObj.getJSONArray("models");
                    for (Object modelItem : modelsArray) {
                        JSONObject modelObj = (JSONObject) modelItem;
                        JSONObject configObj = modelObj.getJSONObject("config");
                        String currentProject = configObj.containsKey("deviceOperationParameter") ?
                                configObj.getJSONObject("deviceOperationParameter").getString("project") : "";
                        if (currentProject.equals(projectName)) {
                            String deviceUniqueCode = configObj.getString("deviceUniqueCode");
                            deviceUniqueCodes.add(deviceUniqueCode);
                        }
                    }
                }
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return deviceUniqueCodes;
    }

    public static Optional<Integer> findDeviceIdByAliasAndProject(String deviceAliasName, String projectName) {
        try (BufferedReader reader = new BufferedReader(new FileReader(DEVICE_CONFIG_PATH))) {
            StringBuilder jsonString = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                jsonString.append(line);
            }

            JSONObject rootObject = JSONObject.from(JSON.parseObject(jsonString.toString()));
            JSONArray devicesArray = rootObject.getJSONArray("devices");

            for (Object deviceItem : devicesArray) {
                JSONObject deviceObj = (JSONObject) deviceItem;
                JSONArray modelsArray = deviceObj.getJSONArray("models");
                for (Object modelItem : modelsArray) {
                    JSONObject modelObj = (JSONObject) modelItem;
                    JSONObject configObj = modelObj.getJSONObject("config");
                    String currentDeviceAliasName = configObj.getString("deviceAliasName");
                    String currentProject = configObj.getJSONObject("deviceOperationParameter").getString("project");
                    if (currentDeviceAliasName.equals(deviceAliasName) && currentProject.equals(projectName)) {
                        return Optional.of(configObj.getIntValue("id"));
                    }
                }
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

        return Optional.empty();
    }


    private List<String> getRobotUniqueCodeList() {
        String projectName = mainModel.getAppInfo().getProject();
        return findUniqueCodesByDeviceTypeAndProject("robotType", projectName);
    }

    private Optional<Integer> getRobotIdAliasAndProject(String deviceAliasName) {
        String projectName = mainModel.getAppInfo().getProject();
        return findDeviceIdByAliasAndProject(deviceAliasName, projectName);
    }

    private Map<String, List<RobotCoordinates>> getRobotCoordinates() {
        List<String> devicesList = getRobotUniqueCodeList();
        Map<String, List<RobotCoordinates>> deviceRobotCoordinatesMap = new HashMap<>();
        for (String uniqueCode : devicesList) {
            RobotCoordinatesQuery robotCoordinatesQuery = new RobotCoordinatesQuery();
            robotCoordinatesQuery.setProjectName(mainModel.getAppInfo().getProject());
            robotCoordinatesQuery.setDeviceUniqueCode(uniqueCode);
            JsonResponse<List<RobotCoordinates>> resp = defaultPostJsonResponse(UrlConstants.RobotCoordinatesUrls.GET_ALL_ROBOT_COORDINATES,
                    robotCoordinatesQuery, new TypeReference<JsonResponse<List<RobotCoordinates>>>() {
                    });
            if (resp.isOk()) {
                deviceRobotCoordinatesMap.put(uniqueCode, resp.getData());
            }
        }
        return deviceRobotCoordinatesMap;
    }

    private ScreenConfig getScreenConfig() {
        String projectName = mainModel.getAppInfo().getProject();
        return OperationTargetHolder.getScreenKit().loadConfig(projectName);
    }

    private List<PercentTemplateRoi> getPercentTemplateRoi() {
        String projectName = mainModel.getAppInfo().getProject();
        TemplateRoiQuery templateRoiQuery = new TemplateRoiQuery();
        templateRoiQuery.setProjectName(projectName);
        Set<Device> devices = mainModel.getDeviceManageModel().getDevices();
        //TODO：传入连接的相机设备的唯一码
        templateRoiQuery.setDeviceUniqueCode("");
        return OperationTargetHolder.getRoiKit().fetchAllPercentTemplateRoiList(templateRoiQuery);
    }

    public static List<String> getSelectedJsonFiles(String directoryPath, List<String> jsonFileNames) {
        List<String> jsonFilePaths = new ArrayList<>();
        File directory = new File(directoryPath);
        if (directory.isDirectory()) {
            File[] files = directory.listFiles((dir, name) -> {
                int lastDotIndex = name.lastIndexOf('.');
                if (lastDotIndex == -1) {
                    return false;
                }
                String baseName = name.substring(0, lastDotIndex);
                return jsonFileNames.contains(baseName) && name.endsWith(".json");
            });
            if (files != null) {
                for (File file : files) {
                    jsonFilePaths.add(file.getAbsolutePath());
                }
            }
        }
        return jsonFilePaths;
    }

}
