package ui.layout.left.display.components.tappane.case_mgmt.excelcase;

import cn.hutool.core.util.ArrayUtil;
import common.utils.StringUtils;
import excelcase.AutoScrollPane;
import excelcase.config.json.*;
import llm.ActionSequenceLlmScriptGenerator;
import llm.ActionSequenceRequest;
import llm.LLMException;
import llm.SystemPrompt;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.jetbrains.annotations.NotNull;
import sdk.base.JsonResponse;
import sdk.domain.action_sequence.ActionSequenceCheckReporter;
import sdk.domain.action_sequence.ActionSequenceCheckResult;
import sdk.domain.action_sequence.ActionSequenceContext;
import sdk.domain.action_sequence.ActionSequenceSimpleContext;
import sdk.domain.excel.ExcelSheetData;
import sdk.entity.OperationTargetHolder;
import ui.base.BaseView;
import ui.base.NamedSwingWorker;
import ui.base.cosntants.ColumnNameConstants;
import ui.base.dialogs.*;
import ui.base.renderer.*;
import ui.base.table.FrozenColumnTable;
import ui.base.table.TableRowTransferHandler;
import ui.base.table.checkbox.CheckboxTable;
import ui.callback.TableMenuCallback;
import ui.entry.ClientView;
import ui.layout.aichat.UserPromptPanel;
import ui.layout.left.display.components.tappane.case_mgmt.excelcase.context.SingleCaseContext;
import ui.layout.right.components.log.LogCmd;
import ui.layout.right.components.log.LogMessage;
import ui.layout.right.components.log.LogOutputPanelView;
import ui.layout.right.components.taskManagement.TaskEventObserver;
import ui.layout.right.components.taskManagement.TaskFile;
import ui.layout.right.components.testcase.TestStep;
import ui.layout.right.components.testcase.TestStepEventObserver;
import ui.model.MainModel;
import ui.model.testcase.TestCaseTableEventObserver;
import ui.utils.SwingUtil;

import javax.annotation.concurrent.ThreadSafe;
import javax.swing.Timer;
import javax.swing.*;
import javax.swing.table.*;
import javax.swing.text.JTextComponent;
import java.awt.*;
import java.awt.event.*;
import java.io.File;
import java.io.IOException;
import java.net.ConnectException;
import java.util.List;
import java.util.*;
import java.util.concurrent.CancellationException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static common.constant.KeyStrokeConstants.Find_KEY_STROKE;
import static common.constant.KeyStrokeConstants.Locate_KEY_STROKE;
import static common.utils.ListUtils.*;
import static common.utils.StringUtils.reorderLinesWithNewNumbers;
import static ui.base.cosntants.ExcelConstants.*;
import static ui.utils.SwingUtil.openAndSelectedFilePath;
import static ui.utils.SwingUtil.openFileFolder;


/**
 * Excel测试用例表格组件
 * 提供以下功能:
 * 1. 表格数据的渲染和展示
 * 2. 用例执行和结果验证
 * 3. 数据持久化
 */
@Slf4j
@ThreadSafe
public class ExcelCaseTable extends CheckboxTable<ExcelRow>
        implements BaseView, TestStepEventObserver, TaskEventObserver, TableMenuCallback<ExcelRow>, CustomTableCellEditor,
        TestCaseTableEventObserver, TableRowTransferHandler.DragRowsEventListener {
    @Setter
    @Getter
    private boolean rendering = false;
    private final MainModel mainModel;
    @Getter
    private MultiLineCellRenderer multiLineCellRenderer;
    private final ExcelCaseRenderTabbedPane excelCaseRenderTabbedPane;
    private final ExcelCaseControlPanel excelCaseControlPanel;
    private final Map<Integer, ActionSequenceCheckReporter> actionSequenceCheckReporterManager;
    private final ClientView clientView;
    @Setter
    @Getter
    private String sheetName;
    @Setter
    private boolean sync = true;
    @Getter
    @Setter
    private AutoScrollPane autoScrollPane;
    @Setter
    private boolean isEditing = false;
    @Getter
    @Setter
    private int testingRow;
    private final ExcelCaseTabPaneView excelCaseTabPaneView;
    private JProgressBar progressBar;
    private JButton stopProgressButton;
    private JButton pauseResumeButton;
    @Getter
    private SwingWorker<Void, CellGroup> convertTaskWorker;
    private TableRowTransferHandler tableRowTransferHandler;
    private final List<int[]> matchPositions = new ArrayList<>(); // 存储所有匹配项的位置
    private int currentMatchIndex = 0; // 当前选中的匹配项索引
    @Setter
    private LogOutputPanelView logOutputPanelView;
    private JDialog findDialog;
    private JTextField searchTextField;
    private final Map<String, Object> selectedRowData = new HashMap<>();
    @Getter
    private Set<Integer> hiddenRows = new HashSet<>();
    private TableRowSorter<TableModel> sorter;
    private RowFilter<Object, Object> filter;
    // 定义正则表达式，支持中文字符，并使用负向前瞻确保不匹配 NoExist
    private static final Pattern PATTERN_RECOGNITION_EXIST = Pattern.compile("Pattern-(?!NoExist-)([^\\-\\n]+)");
    private static final Pattern OCR_RECOGNITION_EXIST = Pattern.compile("OCR-(?!NoExist-)([^\\-\\n]+)");

    // 区分 NoExist 模式，只提取 - 后面的部分，确保这部分内容不包含 - 和换行符
    private static final Pattern PATTERN_RECOGNITION_NO_EXIST = Pattern.compile("Pattern-NoExist-([^\\-\\n]+)");
    private static final Pattern OCR_RECOGNITION_NO_EXIST = Pattern.compile("OCR-NoExist-([^\\-\\n]+)");


    private static final List<Pattern> patternList = Arrays.asList(
            PATTERN_RECOGNITION_EXIST,
            OCR_RECOGNITION_EXIST,
            PATTERN_RECOGNITION_NO_EXIST,
            OCR_RECOGNITION_NO_EXIST
    );
    private static final String[] COLUMN_NAMES = {"Expected Test Sequences" + "\n" + "预期测试序列结果", "Actual Result"};
    private List<TemplateRow> selectedRowTemplateList = new ArrayList<>();
    private List<TemplateRow> selectedRowFailPicList = new ArrayList<>();

    private int fontSize = 12;

    private int cycleTimes;
    private Timer inactivityTimer;
    private static final int INACTIVITY_DELAY = 30000; // 60秒
    private boolean isAutoScrollingPaused = false;
    @Getter
    private FrozenColumnTable frozenColumnTable;
    private final UndoManager undoManager = new UndoManager();
    @Setter
    private boolean undoInProgress = false;

    @Getter
    private final Object pauseLock;

    public void setValueDirectly(int row, int column, Object value) {
        super.setValueAt(value, row, column);
        excelCaseTabPaneView.updateTitleCaseSaveStatus(false);
    }

    public void clearActionSequenceCheckReporter() {
        actionSequenceCheckReporterManager.clear();
    }

    private void showFindDialog() {
        if (findDialog == null || !findDialog.isVisible()) {
            findDialog = new JDialog((JFrame) SwingUtilities.getWindowAncestor(ExcelCaseTable.this), "查找", false);
            findDialog.setSize(350, 180);

            // 设置对话框内容
            JLabel label = new JLabel("请输入要查找的内容:");
            searchTextField = new JTextField(25);
            JButton okButton = new JButton("确定");
            JButton nextButton = new JButton("下一个");
            JButton previousButton = new JButton("上一个");
            JButton cancelButton = new JButton("取消");

            // 添加事件监听器
            okButton.addActionListener(e -> {
                String searchText = searchTextField.getText().trim();
                if (!searchText.isEmpty()) {
                    performSearch(searchText);
                } else {
                    JOptionPane.showMessageDialog(findDialog, "输入为空！", "查找结果", JOptionPane.INFORMATION_MESSAGE);
                }
            });

            nextButton.addActionListener(e -> nextMatch());
            previousButton.addActionListener(e -> previousMatch());
            cancelButton.addActionListener(e -> findDialog.dispose());

            // 布局管理器
            findDialog.setLayout(new BorderLayout());
            JPanel inputPanel = new JPanel(new GridLayout(2, 1));
            inputPanel.add(label);
            inputPanel.add(searchTextField);
            findDialog.add(inputPanel, BorderLayout.CENTER);

            JPanel buttonPanel = new JPanel();
            buttonPanel.add(okButton);
            buttonPanel.add(nextButton);
            buttonPanel.add(previousButton);
            buttonPanel.add(cancelButton);
            findDialog.add(buttonPanel, BorderLayout.SOUTH);

            // 设置对话框位置
            findDialog.setLocationRelativeTo(null); // 相对于当前组件居中

            // 显示对话框
            findDialog.setVisible(true);
        } else {
            findDialog.toFront();
        }
    }

    private void performSearch(String searchText) {
        matchPositions.clear();
        for (int row = 0; row < getRowCount(); row++) {
            for (int col = 0; col < getColumnCount(); col++) {
                Object value = getValueAt(row, col);
                if (value != null && value.toString().contains(searchText)) {
                    matchPositions.add(new int[]{row, col});
                }
            }
        }

        if (!matchPositions.isEmpty()) {
            currentMatchIndex = 0;
            highlightMatch(currentMatchIndex);
        } else {
            JOptionPane.showMessageDialog(findDialog, "未找到匹配的项", "查找结果", JOptionPane.INFORMATION_MESSAGE);
        }
    }

    private void highlightMatch(int index) {
        if (index >= 0 && index < matchPositions.size()) {
            int[] position = matchPositions.get(index);
            int row = position[0];
            int col = position[1];
            setRowSelectionInterval(row, row);
            setColumnSelectionInterval(col, col);
            scrollRectToVisible(getCellRect(row, col, true));
        }
    }

    private void nextMatch() {
        if (!matchPositions.isEmpty()) {
            currentMatchIndex = (currentMatchIndex + 1) % matchPositions.size();
            highlightMatch(currentMatchIndex);
        } else {
            JOptionPane.showMessageDialog(findDialog, "未找到匹配的项", "查找结果", JOptionPane.INFORMATION_MESSAGE);
        }
    }

    private void previousMatch() {
        if (!matchPositions.isEmpty()) {
            currentMatchIndex = (currentMatchIndex - 1 + matchPositions.size()) % matchPositions.size();
            highlightMatch(currentMatchIndex);
        } else {
            JOptionPane.showMessageDialog(findDialog, "未找到匹配的项", "查找结果", JOptionPane.INFORMATION_MESSAGE);
        }
    }

    private void bindKeys() {
        // 定义动作
        Action findAction = new AbstractAction() {
            @Override
            public void actionPerformed(ActionEvent e) {
                showFindDialog();
            }
        };

        Action nextAction = new AbstractAction() {
            @Override
            public void actionPerformed(ActionEvent e) {
                nextMatch();
            }
        };

        Action previousAction = new AbstractAction() {
            @Override
            public void actionPerformed(ActionEvent e) {
                previousMatch();
            }
        };
        // 绑定动作到按键组合
        getInputMap(JComponent.WHEN_IN_FOCUSED_WINDOW).put(KeyStroke.getKeyStroke(KeyEvent.VK_F, InputEvent.CTRL_DOWN_MASK), "find");
        getActionMap().put("find", findAction);

        getInputMap(JComponent.WHEN_IN_FOCUSED_WINDOW).put(KeyStroke.getKeyStroke(KeyEvent.VK_N, InputEvent.CTRL_DOWN_MASK), "next");
        getActionMap().put("next", nextAction);

        getInputMap(JComponent.WHEN_IN_FOCUSED_WINDOW).put(KeyStroke.getKeyStroke(KeyEvent.VK_P, InputEvent.CTRL_DOWN_MASK), "previous");
        getActionMap().put("previous", previousAction);

    }

    public ExcelCaseTable(MainModel mainModel, ClientView clientView, ExcelCaseTabPaneView excelCaseTabPaneView) {
        this.mainModel = mainModel;
        this.clientView = clientView;
        this.excelCaseTabPaneView = excelCaseTabPaneView;
        this.excelCaseRenderTabbedPane = excelCaseTabPaneView.getExcelCaseRenderTabbedPane();
        this.excelCaseControlPanel = excelCaseTabPaneView.getExcelCaseControlPanel();
        actionSequenceCheckReporterManager = new HashMap<>();
        this.pauseLock = excelCaseTabPaneView.getPauseLock();
        registerModelObservers();
        createActions();
        createMenu();
        initData();   //add by lhy
        initProgressBarAndStopButton(); //add by lpf
        initDragRows();
        ctrlGShortcut();
        bindKeys();
        setupInactivityTimer();//暂停滚动定时器
        setFocusable(true); // 确保表格可以接收键盘事件
        setRowHideFilter();     //使用RowFilter来支持筛选功能，隐藏和恢复JTable行
        // 添加Ctrl+S保存快捷键
        KeyStroke saveKeyStroke = KeyStroke.getKeyStroke(KeyEvent.VK_S, InputEvent.CTRL_DOWN_MASK);
        getInputMap(JComponent.WHEN_IN_FOCUSED_WINDOW).put(saveKeyStroke, "save");
        getActionMap().put("save", new AbstractAction() {
            @Override
            public void actionPerformed(ActionEvent e) {
                // 触发保存操作，假设需要传递true表示执行保存
                boolean isSave = true;
                mainModel.getTestCaseTableModel().saveExcelCaseTable(isSave);
            }
        });
        // 定义快捷键 Ctrl+Z 和 Ctrl+Y
        KeyStroke undoKeyStroke = KeyStroke.getKeyStroke(KeyEvent.VK_Z, InputEvent.CTRL_DOWN_MASK);
        KeyStroke redoKeyStroke = KeyStroke.getKeyStroke(KeyEvent.VK_Y, InputEvent.CTRL_DOWN_MASK);
        // 绑定快捷键到输入映射（全局生效）
        getInputMap(JComponent.WHEN_IN_FOCUSED_WINDOW).put(undoKeyStroke, "undo");
        getInputMap(JComponent.WHEN_IN_FOCUSED_WINDOW).put(redoKeyStroke, "redo");
        // 绑定动作名称到动作映射
        getActionMap().put("undo", new AbstractAction() {
            @Override
            public void actionPerformed(ActionEvent e) {
                if (undoManager.canUndo()) {
                    handleUndo();
                }
            }
        });

        getActionMap().put("redo", new AbstractAction() {
            @Override
            public void actionPerformed(ActionEvent e) {
                if (undoManager.canRedo()) {
                    handleRedo();
                }
            }
        });
    }

    private void setRowHideFilter() {
        sorter = new TableRowSorter<TableModel>(getModel()) {
            @Override
            public void toggleSortOrder(int column) {
                // 空实现 - 禁用点击表头排序
            }

            @Override
            public void setSortKeys(List<? extends SortKey> keys) {
                // 空实现 - 禁用程序化排序
            }
        };
        setRowSorter(sorter);
        // 创建过滤器
        filter = new RowFilter<Object, Object>() {
            @Override
            public boolean include(Entry<? extends Object, ? extends Object> entry) {
                // 获取当前行的模型索引
                int modelRow = Integer.parseInt(entry.getIdentifier().toString());
                // 如果不在隐藏集合中，则包含此行
                return !hiddenRows.contains(modelRow);
            }
        };
        // 应用过滤器
        sorter.setRowFilter(filter);
    }


    public void removeHiddenRow(int row) {  // 第2行模型索引为1
        if (!hiddenRows.contains(row)) {
            return;
        }
        hiddenRows.remove(row);
    }

    public void addHiddenRow(int row) {  // 第2行模型索引为1
        if (hiddenRows.contains(row)) {
            return;
        }
        hiddenRows.add(row);
    }

    public void applyFiler() {
        sorter.setRowFilter(filter);
        // 同步更新 FrozenColumnTable
        if (frozenColumnTable != null) {
            frozenColumnTable.syncAfterFilter();
        }
    }

    public void clearAllHiddenRows() {
        // 清除过滤器（显示所有行）
        sorter.setRowFilter(null);
        // 或者清空隐藏行集合
        hiddenRows.clear();
        sorter.setRowFilter(filter); // 重新应用空过滤器
        // 同步更新 FrozenColumnTable
        if (frozenColumnTable != null) {
            frozenColumnTable.syncAfterFilter();
        }
    }

    private void handleUndo() {
        // 强制结束编辑状态
        if (isEditing()) {
            TableCellEditor editor = getCellEditor();
            if (editor != null) {
                editor.stopCellEditing(); // 结束编辑
            }
        }

        // 执行撤销操作
        undoManager.undo(this);
    }

    private void handleRedo() {
        // 强制结束编辑状态
        if (isEditing()) {
            TableCellEditor editor = getCellEditor();
            if (editor != null) {
                editor.stopCellEditing(); // 结束编辑
            }
        }

        // 执行重做操作
        undoManager.redo(this);
    }

    private void setupInactivityTimer() {
        inactivityTimer = new Timer(INACTIVITY_DELAY, e -> restoreNormalScrolling());
        inactivityTimer.setRepeats(false);
    }

    private void restoreNormalScrolling() {
        // 恢复正常的纵向滚动行为
        isAutoScrollingPaused = false;
    }

    public void pauseAutoScrolling() {
        if (!isAutoScrollingPaused) {
            isAutoScrollingPaused = true;
        }
    }

    private void resetInactivityTimer() {
        if (inactivityTimer.isRunning()) {
            inactivityTimer.restart();
        } else {
            inactivityTimer.start();
        }
    }

    private void initProgressBarAndStopButton() {
        progressBar = excelCaseTabPaneView.getProgressBar();
        progressBar.setStringPainted(true);
        stopProgressButton = excelCaseTabPaneView.getStopProgressButton();
        pauseResumeButton = excelCaseTabPaneView.getPauseResumeButton();
    }

    private void initDragRows() {
        new Thread(() -> {
            tableRowTransferHandler = new TableRowTransferHandler(this);
            tableRowTransferHandler.addDragRowsEventListener(this);
            setDragEnabled(true);
            setDropMode(DropMode.INSERT_ROWS);
            setTransferHandler(tableRowTransferHandler);
        }).start();
    }

    @Override
    protected void setTableRenderer() {
        multiLineCellRenderer = new MultiLineCellRenderer();
        setDefaultRenderer(Object.class, multiLineCellRenderer);
        setDefaultEditor(Object.class, new MultiLineCellEditor(this, this));
//        // 创建下拉框组件
//        JComboBox<String> comboBox = new JComboBox<>(new String[]{"YES", "NO"});
//        // 设置第二列的编辑器为组合框
//        TableColumn column = this.getColumnModel().getColumn(3);
//        column.setCellEditor(new DefaultCellEditor(comboBox));
//        // 可选：设置第二列的渲染器为组合框，使其看起来更像下拉框
//        column.setCellRenderer(new ComboBoxRenderer());
    }

    @Override
    protected String[] getColumns() {
        return new String[]{
                checkBox.getColumnName()
        };
    }

    @Override
    protected void setDefaultTableHeader() {
        setTableHeader(new CustomTableHeader(getColumnModel()));
    }

    private void setupMouseListener() {
        //mousePressed -> mouseReleased -> mouseClicked
        MouseAdapter mouseAdapter = new MouseAdapter() {
            private boolean pressedInComponent = false;

            @Override
            public void mousePressed(MouseEvent e) {
                pauseAutoScrolling();
                resetInactivityTimer();
                pressedInComponent = true;
                if (e.isControlDown()) {
                    isEditing = true;
                }
                super.mousePressed(e);
            }

            @Override
            public void mouseReleased(MouseEvent e) {
                pauseAutoScrolling();
                resetInactivityTimer();
                if (pressedInComponent) {
                    handleMouseClick(e); // 在这里处理点击逻辑
                }
                pressedInComponent = false;
                handleMouseRelease(e);
                super.mouseReleased(e);
            }

            @Override
            public void mouseClicked(MouseEvent e) {
                pauseAutoScrolling();
                resetInactivityTimer();
            }
        };
        this.addMouseListener(mouseAdapter);
        this.addMouseMotionListener(mouseAdapter);
        this.addHierarchyListener(e -> {
            if ((e.getChangeFlags() & HierarchyEvent.PARENT_CHANGED) != 0) {
                Component parent = ExcelCaseTable.this.getParent();
                while (parent != null && !(parent instanceof JScrollPane)) {
                    parent = parent.getParent();
                }
                if (parent != null) {
                    autoScrollPane = (AutoScrollPane) parent;
                    autoScrollPane.addMouseListener(mouseAdapter);
                    autoScrollPane.addMouseMotionListener(mouseAdapter);
                }
            }
        });
    }

    private void setupWheelListener() {
        addMouseWheelListener(e -> {
            pauseAutoScrolling();
            resetInactivityTimer();
            if (e.isControlDown()) {
                int notches = e.getWheelRotation();
                if (notches < 0) {
                    fontSize += 1;
                } else {
                    fontSize -= 1;
                }
                fontSize = fontSize <= 0 ? 1 : fontSize;
                setFont(getFont().deriveFont((float) fontSize));
                //增加表格行自适应高度
                for (int row = 0; row < getRowCount(); row++) {
                    updateRowHeight(row);
                    updateFrozenHeight(row);
                }
            } else {
                getParent().dispatchEvent(SwingUtilities.convertMouseEvent(this, e, getParent()));

            }
        });
    }

    private void handleMouseClick(MouseEvent e) {
        if (!(e.getSource() instanceof JTable)) {
            return;
        }

        JTable table = (JTable) e.getSource();
        Point clickPoint = e.getPoint();
        int row = table.rowAtPoint(clickPoint);
        int viewColumn = table.columnAtPoint(clickPoint);

        // 检查点击位置是否有效
        if (row < 0 || viewColumn < 0) {
            return;
        }
        createTestStep(row, viewColumn);

        handleSingleClick(table, row, viewColumn);
        if (e.getClickCount() == 2) {
            doubleClick(row, viewColumn);
        }
    }

    private void handleSingleClick(JTable table, int row, int viewColumn) {
        try {
            String headerValue = getColumnHeaderValue(viewColumn);
            changeTestStepBackgroundColor(headerValue);
            updateLogOutput(row);
            processSelectedRowData(table, row);
        } catch (Exception ex) {
            // 添加适当的错误处理，比如日志记录
            log.error("单步点击错误", ex);
        }
    }

    private void handleMouseRelease(MouseEvent e) {
        if (isEditing) {
            Point point = e.getPoint();
            int editRow = rowAtPoint(point);
            int editCol = columnAtPoint(point);

            if (editRow >= 0 && editCol >= 0) {
                setRowSelectionInterval(editRow, editRow);
                edit(editRow, editCol);
            }
            isEditing = false;
        }
    }

    private String getColumnHeaderValue(int viewColumn) {
        return (String) getColumnModel().getColumn(viewColumn).getHeaderValue();
    }

    private void updateLogOutput(int row) {
        logOutputPanelView = clientView.getTestStepView().getLogOutputPanelView();
        if (logOutputPanelView != null) {
            logOutputPanelView.followLog(row);
        }
    }

    private void processSelectedRowData(JTable table, int row) {
        int modelRow = convertRowIndexToModel(row);
        processRowData(table, modelRow);
    }

    @Override
    public void createActions() {
        super.createActions();
        // 添加鼠标事件监听器
        setupMouseListener();
        // 添加鼠标滚轮事件监听器
        setupWheelListener();
    }

    private void processRowData(JTable table, int rowIndex) {
        // 清空列表
        selectedRowData.clear();
        selectedRowTemplateList.clear();
        selectedRowFailPicList.clear();

        // 获取表格模型
        TableModel model = table.getModel();
        int columnCount = model.getColumnCount();

        // 遍历每一列，获取该行的数据
        for (int col = 0; col < columnCount; col++) {
            Object value = model.getValueAt(rowIndex, col);
            TableColumn tableColumn = table.getColumnModel().getColumn(col);
            int modelColumnIndex = tableColumn.getModelIndex(); // 获取模型中的列索引
            String columnName = model.getColumnName(modelColumnIndex);
            selectedRowData.put(columnName, value);
        }

        if (selectedRowData.containsKey(COLUMN_NAMES[0]) && selectedRowData.get(COLUMN_NAMES[0]) != null) {
            selectedRowTemplateList = extractTemplates((String) selectedRowData.get(COLUMN_NAMES[0]));
            if (selectedRowData.containsKey(COLUMN_NAMES[1]) && selectedRowData.get(COLUMN_NAMES[1]) != null) {
                selectedRowFailPicList = extractFailPictures((String) selectedRowData.get(COLUMN_NAMES[1]));
            }
            log.info("选中行的模板数据: {}", selectedRowTemplateList);
            log.info("选中行的失败图像数据: {}", selectedRowFailPicList);
        }
        mainModel.getTestCaseTableModel().showTemplateInfo(selectedRowTemplateList, selectedRowFailPicList);
    }

    private List<TemplateRow> extractTemplates(String input) {
        List<TemplateRow> templateRows = new ArrayList<>();
        Pattern devicePattern = Pattern.compile("(Capture|Vision)(?:#(\\d+))?(-.*)?");
        Matcher deviceMatcher = devicePattern.matcher(input);
        for (Pattern pattern : patternList) {
            Matcher matcher = pattern.matcher(input);
            while (matcher.find() && deviceMatcher.find()) {
                String templateName = matcher.group(1);
                String prefix = deviceMatcher.group(1); // Capture 或 Vision
                String number = deviceMatcher.group(2); // #后面的数字，可能为null
                if (templateName != null && !templateName.isEmpty()) {
                    // 如果没有匹配到数字，则默认补充为 #1
                    if (number == null || number.isEmpty()) {
                        templateRows.add(new TemplateRow(templateName, prefix, "1", ""));
                    } else {
                        templateRows.add(new TemplateRow(templateName, prefix, number, ""));
                    }

                }
            }
        }
        return templateRows;
    }

    private List<TemplateRow> extractFailPictures(String input) {
        List<TemplateRow> failRows = new ArrayList<>();
        Pattern pattern = Pattern.compile("失败图片路径：([^\\n]+?)\\s*-\\(\\d+/\\d+\\)", Pattern.MULTILINE);
        Matcher matcher = pattern.matcher(input);
        while (matcher.find()) {
            String imagePath = matcher.group(1).trim();
            if (!imagePath.isEmpty()) {
                Pattern failPattern = Pattern.compile("failPictures\\\\([^\\n\\-]+)\\\\");
                Matcher failMatcher = failPattern.matcher(imagePath);
                if (failMatcher.find()) {
                    String matchedGroup = failMatcher.group(1);
                    if (matchedGroup != null) {
                        // 正则表达式：匹配 \failPictures 前一个 \ 后的内容
                        Pattern deviceNamePattern = Pattern.compile("\\\\([^\\\\]+?)\\\\failPictures");
                        // 创建 Matcher 对象
                        Matcher deviceNameMatcher = deviceNamePattern.matcher(imagePath);
                        if (deviceNameMatcher.find()) {
                            String deviceName = deviceNameMatcher.group(1);
                            if (deviceName != null) {
                                failRows.add(new TemplateRow(matchedGroup, deviceName, deviceName, imagePath));
                            }
                        }
                    }
                }
            }
        }
        return failRows;
    }

    public void slideToRow(int row) {
        if (autoScrollPane != null) {
            autoScrollPane.scrollToRow(row);
        }
    }

    public ActionSequenceSimpleContext combineSimpleActionSequenceContext(int row) {
        ActionSequenceSimpleContext actionSequenceContext = new ActionSequenceSimpleContext();
        actionSequenceContext.setPrecondition(excelCaseRenderTabbedPane.getValueByColumnName(row, ColumnNameConstants.getInstance().getInitialCondition()));
        actionSequenceContext.setOperationStep(excelCaseRenderTabbedPane.getValueByColumnName(row, ColumnNameConstants.getInstance().getAction()));
        actionSequenceContext.setExpectResult(excelCaseRenderTabbedPane.getValueByColumnName(row, ColumnNameConstants.getInstance().getExpectedResult()));
        return actionSequenceContext;
    }

    //FIXME,耦合了getTestStepView
    public ActionSequenceContext combineActionSequenceContext(int row) {
        setRowSelectionInterval(row, row);
        ActionSequenceContext actionSequenceContext = new ActionSequenceContext();
        try {
            actionSequenceContext.setSequenceType(ActionSequenceContext.ALL);
            //TODO：要改成从ExcelCaseTable拿三列数据——LHY
            actionSequenceContext.setPrecondition(clientView.getTestStepView().getTestStepTable().getTestStepByType(ColumnNameConstants.getInstance().getPreconditionSequences()));
            actionSequenceContext.setOperationStep(clientView.getTestStepView().getTestStepTable().getTestStepByType(ColumnNameConstants.getInstance().getOperationStepSequences()));
            actionSequenceContext.setExpectResult(clientView.getTestStepView().getTestStepTable().getTestStepByType(ColumnNameConstants.getInstance().getExpectedResultSequences()));
            int testNoColumnId = findTableColumnIndexByName(ColumnNameConstants.getInstance().getNo());
            int testCaseIDColumnId = findTableColumnIndexByName(ColumnNameConstants.getInstance().getTestCaseID());
            int testKeyColumnId = findTableColumnIndexByName(ColumnNameConstants.getInstance().getTestKey());
            double testNo = 0;
            if (testNoColumnId != -1) {
                Object testNoValue = getValueAt(row, testNoColumnId);
                String testNoStr = testNoValue == null ? "" : testNoValue.toString();
                if (!StringUtils.isEmpty(testNoStr)) {
                    testNo = Double.parseDouble(testNoStr);
                }
            }
            String tcID = "";
            if (testCaseIDColumnId != -1) {
                Object tcIdValue = getValueAt(row, testCaseIDColumnId);
                tcID = tcIdValue == null ? "" : tcIdValue.toString();
            }
            String testKey = "";
            if (testKeyColumnId != -1) {
                Object testKeyValue = getValueAt(row, testKeyColumnId);
                testKey = testKeyValue == null ? "" : testKeyValue.toString();
            }
            actionSequenceContext.setTestNo(testNo);
            actionSequenceContext.setTcId(tcID);
            actionSequenceContext.setTestKey(testKey);
        } catch (Exception e) {
            log.error("组合动作序列信息异常", e);
        }
        return actionSequenceContext;
    }

    @Override
    public void setValueAt(Object aValue, int row, int column) {
        // 获取旧值
        Object oldValue = getValueAt(row, column);

        // 调用父类方法设置新值
        super.setValueAt(aValue, row, column);

        // 如果不是撤销操作触发的修改，记录到撤销栈
        if (!undoInProgress && !Objects.equals(oldValue, aValue)) {
            undoManager.addEdit(new ExcelCaseTableEdit(row, column, oldValue, aValue));
        }

        // 清除该行的行高缓存，因为数据变化可能影响行高
        if (rowHeightCache != null) {
            rowHeightCache.remove(row);
        }

        // 原有的业务逻辑
        excelCaseTabPaneView.updateTitleCaseSaveStatus(false);
    }

    /**
     * 双击行
     *
     * @param row 行
     */
    private void doubleClick(int row, int column) {
        clearSelection();
        String columnName = getColumnName(column);
        log.info("双击列名:{}", columnName);
        ActionSequenceContext actionSequenceContext = new ActionSequenceContext();
        String userColumnName;
        Integer columnIndex = column;
        if (ColumnNameConstants.getInstance().getPreconditionSequences().equals(columnName)) {
            userColumnName = "前提条件";
            actionSequenceContext.setSequenceType(ActionSequenceContext.PRECONDITION);
        } else if (ColumnNameConstants.getInstance().getOperationStepSequences().equals(columnName)) {
            userColumnName = "操作步骤";
            actionSequenceContext.setSequenceType(ActionSequenceContext.OPERATION_STEP);
        } else if (ColumnNameConstants.getInstance().getExpectedResultSequences().equals(columnName)) {
            userColumnName = "期望结果";
            actionSequenceContext.setSequenceType(ActionSequenceContext.EXPECT_RESULT);
        } else {
            columnIndex = null;
            userColumnName = String.format("第%d行", row + 1);
            actionSequenceContext.setSequenceType(ActionSequenceContext.ALL);
        }
        actionSequenceContext.setPrecondition(clientView.getTestStepView().getTestStepTable().getTestStepByType(ColumnNameConstants.getInstance().getPreconditionSequences()));
        actionSequenceContext.setOperationStep(clientView.getTestStepView().getTestStepTable().getTestStepByType(ColumnNameConstants.getInstance().getOperationStepSequences()));
        actionSequenceContext.setExpectResult(clientView.getTestStepView().getTestStepTable().getTestStepByType(ColumnNameConstants.getInstance().getExpectedResultSequences()));
        SingleCaseContext singleCaseContext = new SingleCaseContext();
        singleCaseContext.setRow(row);
        singleCaseContext.setColumn(columnIndex);
        singleCaseContext.setUserColumnName(userColumnName);
        singleCaseContext.setExcelCaseTable(this);
        singleCaseContext.setActionSequenceContext(actionSequenceContext);
//        mainModel.getTestStepModel().switchPanel(false);
        mainModel.getTestCaseTableModel().startTest(singleCaseContext);
    }

    public ActionSequenceCheckReporter executeActionSequence(int row, String userColumnName, ActionSequenceContext actionSequenceContext) {
        return executeActionSequence(row, null, userColumnName, actionSequenceContext);
    }

    public ActionSequenceCheckReporter executeActionSequence(int row, Integer column, String columnName, ActionSequenceContext actionSequenceContext) {
        SwingUtil.invokeLater(() -> {
            clearColor(row);
            if (column == null) {
                //整行执行
                mainModel.getTestStepModel().clearTestStepErrorColor();
            }
            setColor(row, column, TESTING_COLOR);
            repaint();
        });
        LogCmd.getInstance().printLog(LogMessage.info("---------------------------------------"));
        LogCmd.getInstance().printLog(LogMessage.info(String.format("\"%s\"动作序列执行中...", columnName)));
        return checkAndExecuteActionSequenceGrammar(actionSequenceContext);
    }

    public ActionSequenceCheckReporter executeSingleActionSequence(Integer row, Integer column, String columnName, ActionSequenceContext actionSequenceContext) {
        clearColor(row);
        if (column == null) {
            //整行执行
            mainModel.getTestStepModel().clearTestStepErrorColor();
        }
        SwingUtil.invokeLater(() -> {
            setColor(row, column, TESTING_COLOR);
            repaint();
        });
        LogCmd.getInstance().printLog(LogMessage.info("---------------------------------------"));
        LogCmd.getInstance().printLog(LogMessage.info(String.format("\"%s\"动作序列执行中...", columnName)));
        ActionSequenceCheckReporter actionSequenceCheckReporter = checkAndExecuteSingleActionSequenceGrammar(actionSequenceContext);
        printActionSequenceReport(columnName, actionSequenceCheckReporter, false);
        renderActionSequenceGrammarCheckResult(row, column, actionSequenceCheckReporter, false);
        return actionSequenceCheckReporter;
    }


    //获取每行的uuid
    public String getRowUuid(int row) {
        try {
            return String.valueOf(getValueAt(row, SwingUtil.getColumnIndex(this, UUID_KEY)));
        } catch (Exception e) {
//            log.error("用例表没有列名：uuid");
            return "";
        }
    }

    //获取每行的执行次数
    public int getExcelRowExecuteTimes(int row) {
        try {
            int executionTimes = findTableColumnIndexByName(Execution_TIMES);
            if (executionTimes != -1 && getValueAt(row, executionTimes) != null) {
                String executionTimesString = getValueAt(row, executionTimes).toString();
                if (!StringUtils.isEmpty(executionTimesString)) {
                    return Integer.parseInt(executionTimesString);
                }
            }
            return 1;
        } catch (Exception e) {
//            log.error("用例表没有列名：{}", Execution_TIMES);
            return 1;
        }
    }

    public void printActionSequenceReport(String userColumnName, ActionSequenceCheckReporter actionSequenceCheckReporter, boolean onlyCheck) {
        if (actionSequenceCheckReporter != null) {
            if (actionSequenceCheckReporter.isCheckOk()) {
                log.info("\"{}\"动作序列检查成功", userColumnName);
                if (!onlyCheck) {
                    //继续判断执行结果
                    log.info("\"{}\"动作序列执行{}", userColumnName, actionSequenceCheckReporter.isExecuteOk() ? "成功" : "失败");
                }
            } else {
                log.warn("\"{}\"动作序列检查失败", userColumnName);
                if (actionSequenceCheckReporter.getErrorMessage() != null) {
                    LogCmd.getInstance().printLog(LogMessage.error(actionSequenceCheckReporter.getErrorMessage()));
                }
                LogCmd.getInstance().printLog(LogMessage.error("动作序列错误详情:"));
                for (Map.Entry<String, String> entry : actionSequenceCheckReporter.errors().entrySet()) {
                    if (!StringUtils.isBlank(entry.getValue())) {
                        LogCmd.getInstance().printLog(LogMessage.error(String.format("%s", entry.getValue())));
                    }
                }
            }
            LogCmd.getInstance().printLog(LogMessage.info("---------------------------------------"));
        }
    }

    /**
     * 检查并执行动作序列语法
     */
    public ActionSequenceCheckReporter checkAndExecuteActionSequenceGrammar(ActionSequenceContext actionSequenceContext) {
        log.info("开始检查和执行动作序列语法:{}", actionSequenceContext.getRow() + 1);
        ActionSequenceCheckReporter actionSequenceCheckReporter = null;
        JsonResponse<ActionSequenceCheckReporter> response = OperationTargetHolder.getActionSequenceKit().checkAndExecuteActionSequence(actionSequenceContext);
        if (response.isOk()) {
            actionSequenceCheckReporter = response.getData();
        } else {
            LogCmd.getInstance().printLog(LogMessage.error(response.getMessage()));
        }
        return actionSequenceCheckReporter;
    }

    /**
     * 检查并执行动作序列语法
     */
    public ActionSequenceCheckReporter checkAndExecuteSingleActionSequenceGrammar(ActionSequenceContext actionSequenceContext) {
        ActionSequenceCheckReporter actionSequenceCheckReporter = null;
        JsonResponse<ActionSequenceCheckReporter> response = OperationTargetHolder.getActionSequenceKit().checkAndExecuteSingleActionSequence(actionSequenceContext);
        if (response.isOk()) {
            actionSequenceCheckReporter = response.getData();
        } else {
            LogCmd.getInstance().printLog(LogMessage.error(response.getMessage()));
        }
        return actionSequenceCheckReporter;
    }

    /**
     * 检查动作序列语法
     */
    private ActionSequenceCheckReporter checkActionSequenceGrammar(ActionSequenceContext actionSequenceContext) {
        JsonResponse<ActionSequenceCheckReporter> response = OperationTargetHolder.getActionSequenceKit().checkActionSequenceGrammar(actionSequenceContext);
        if (response.isOk()) {
            return response.getData();
        } else {
            LogCmd.getInstance().printLog(LogMessage.error(response.getMessage()));
            return null;
        }
    }

    @Override
    protected void save() {

    }

    @Override
    protected void swapTableList(Map<Integer, Integer> map) {
        for (Map.Entry<Integer, Integer> entry : map.entrySet()) {
            int fromRow = entry.getKey();
            int toRow = entry.getValue();
            removeRowSelectionInterval(fromRow, fromRow);
            addRowSelectionInterval(toRow, toRow);
            excelCaseTabPaneView.updateTitleCaseSaveStatus(false);
            //保存到数据库中
            ExcelRow excelRow = getRow(fromRow);
            getTableList().remove(fromRow);
            getTableList().add(toRow, excelRow);
            saveOrderDataToDB();
        }
    }

    private void editTableCell() {
        int row = rowAtPoint(mouseClickPoint);
        int col = columnAtPoint(mouseClickPoint);
        edit(row, col);
    }

    private void edit(int row, int col) {
        editCellAt(row, col);
        Component editor = getEditorComponent();
        editor.requestFocus();
        if (editor instanceof JScrollPane) {
            JScrollPane scrollPane = (JScrollPane) editor;
            Component component = scrollPane.getViewport().getView();
            if (component instanceof JTextComponent) {
                JTextArea textArea = (JTextArea) component;
                textArea.requestFocus();
            }
        }
    }

    protected void addExcelCaseTableMenu() {
        List<JMenuItem> testMenuList = new ArrayList<>();
        List<JMenuItem> grammarMenuList = new ArrayList<>();
        List<JMenuItem> caseMenuList = new ArrayList<>();
        List<JMenuItem> openFilePathList = new ArrayList<>();
        String excelLogFileFolder = String.format("D:\\FlyTest\\data\\server\\projects\\%s\\database\\fileDB\\logs\\ExcelCaseLog", mainModel.getAppInfo().getProject());
        String adsToolLogFileFolder = String.format("D:\\FlyTest\\data\\server\\projects\\%s\\database\\fileDB\\logs\\ADSToolLogs", mainModel.getAppInfo().getProject());
        String canLogFileFolder = String.format("D:\\FlyTest\\data\\server\\projects\\%s\\database\\fileDB\\logs\\CANLogs", mainModel.getAppInfo().getProject());
        makePopupMenu("测试范围", null, a -> selectedRangeActivated());
        // 选中多行功能
        makePopupMenu("选中多行", null, a -> {
            int[] selectedRows = getSelectedRows();
            DefaultTableModel model = (DefaultTableModel) getModel();
            for (int row : selectedRows) {
                setRowSelected(row, true);
                model.fireTableCellUpdated(row, 0); // 触发复选框列数据变更通知
            }
            frozenColumnTable.getRowHeaderTable().repaint(); // 强制刷新行头表格
        });

        // 取消选中多行功能
        makePopupMenu("取消选中多行", null, a -> {
            int[] selectedRows = getSelectedRows();
            DefaultTableModel model = (DefaultTableModel) getModel();
            for (int row : selectedRows) {
                setRowSelected(row, false);
                model.fireTableCellUpdated(row, 0); // 触发复选框列数据变更通知
            }
            frozenColumnTable.getRowHeaderTable().repaint(); // 强制刷新行头表格
        });
        makePopupMenu("查找行", Find_KEY_STROKE, a -> showFindDialog());
        makePopupMenu("定位行", Locate_KEY_STROKE, a -> locatingRowDialog());
        makePopupMenu("编辑行          Ctrl + 单击", null, a -> editTableCell());
        makePopupMenu("设置行高", null, a -> showRowHeightDialog());
        makePopupMenu("设置列宽", null, a -> showColumnWidthDialog());
        makePopupMenu("设置执行间隔时间", null, a -> showRowExecuteIntervalTimeDialog());
        addMenuSeparator();
        makePopupMenu("单行转脚本", null, a -> singleRowConvertScript());
        makePopupMenu("多行转脚本", null, a -> {
            List<Integer> excelRowList = getCheckedRows();
            convertScript(excelRowList);
        });

        // 如果有转换任务正在运行，禁用这些菜单项
        boolean enabled = convertTaskWorker == null || convertTaskWorker.isDone();
        updateScriptConversionMenuItems(enabled);

        addMenuSeparator();
        makePopupMenu("脚本重排序", null, a -> scriptReOrdering());
        makePopupMenu("表头设置", null, a -> excelCaseControlPanel.tableHeaderSetting());
        addMenuSeparator();
        makePopupMenu("填充整列", null, a -> paddingContentToColumns());
        makePopupMenu("填充check语法", null, a -> paddingCheckGrammar());
        testMenuList.add(makeMenuItem("开始测试", null, a -> excelCaseControlPanel.startTestCase(false)));
        testMenuList.add(makeMenuItem("暂停测试", null, a -> excelCaseControlPanel.pauseTestCase()));
        testMenuList.add(makeMenuItem("结束测试", null, a -> excelCaseControlPanel.stopTestCase()));
        grammarMenuList.add(makeMenuItem("单表语检", null, a -> excelCaseControlPanel.singleCheck()));
        grammarMenuList.add(makeMenuItem("全部语检", null, a -> excelCaseControlPanel.checkAllTableGrammar()));
        grammarMenuList.add(makeMenuItem("取消语检", null, a -> excelCaseControlPanel.cancelTableCheckGrammar()));
        caseMenuList.add(makeMenuItem("刷新用例", null, a -> excelCaseControlPanel.refreshTestCase()));
        caseMenuList.add(makeMenuItem("保存用例", null, a -> excelCaseControlPanel.saveTestCase()));
        caseMenuList.add(makeMenuItem("加载用例", null, a -> excelCaseControlPanel.loadTestCase()));
        openFilePathList.add(makeMenuItem("打开用例文件夹", null, a -> openAndSelectedFilePath(new File(CaseConfigJson.getInstance().getOriginalExcelCaseFilePath()))));
        openFilePathList.add(makeMenuItem("打开另存用例文件夹", null, a -> openAndSelectedFilePath(new File(CaseConfigJson.getInstance().getTemplateExcelCaseFilePath()))));
        openFilePathList.add(makeMenuItem("打开报告文件夹", null, a -> ExcelCaseControlPanel.openReportFilePath()));
        openFilePathList.add(makeMenuItem("打开ADSTool Log文件夹", null, a -> openFileFolder(new File(adsToolLogFileFolder))));
        openFilePathList.add(makeMenuItem("打开CAN Log文件夹", null, a -> openFileFolder(new File(canLogFileFolder))));
        openFilePathList.add(makeMenuItem("打开Excel Log文件夹", null, a -> openFileFolder(new File(org.apache.commons.lang3.StringUtils.isNotEmpty(CaseConfigJson.getInstance().getLogFilePath())
                ? CaseConfigJson.getInstance().getLogFilePath() : excelLogFileFolder))));
        makeSubmenu("用例测试", testMenuList);
        makeSubmenu("语法检查", grammarMenuList);
        makeSubmenu("案例用例", caseMenuList);
        makeSubmenu("打开文件路径", openFilePathList);
        addMenuSeparator();
        makePopupMenu("表格配置应用到所有表", null, a -> excelCaseControlPanel.tableConfigApplyToAll(this));
        mainModel.getTestCaseTableModel().syncButtonStatus(excelCaseControlPanel.getButtonStatusMap());
    }

    /**
     * 更新脚本转换菜单项的状态
     *
     * @param enabled 是否启用
     */
    private void updateScriptConversionMenuItems(boolean enabled) {
        // 遍历弹出菜单中的所有菜单项
        for (Component component : popupMenu.getComponents()) {
            if (component instanceof JMenuItem) {
                JMenuItem menuItem = (JMenuItem) component;
                String text = menuItem.getText();
                // 更新"单行转脚本"和"多行转脚本"菜单项的状态
                if ("单行转脚本".equals(text) || "多行转脚本".equals(text)) {
                    menuItem.setEnabled(enabled);
                }
            }
        }
    }


    private void showRowExecuteIntervalTimeDialog() {
        RowExecuteIntervalTimeDialog rowExecuteIntervalTimeDialog = new RowExecuteIntervalTimeDialog(mainModel);
        rowExecuteIntervalTimeDialog.setModal(true);//确保弹出的窗口在其他窗口前面
        rowExecuteIntervalTimeDialog.setVisible(true);
    }

    private void paddingCheckGrammar() {
        List<Integer> excelRowList = getCheckedRows();
        int actionIndex = findTableColumnIndexByName(ColumnNameConstants.getInstance().getAction());
        int operationStepSequencesIndex = findTableColumnIndexByName(ColumnNameConstants.getInstance().getOperationStepSequences());
        SwingWorker<Void, CellGroup> worker = new SwingWorker<Void, CellGroup>() {
            @Override
            protected Void doInBackground() throws Exception {
                for (Integer excelRow : excelRowList) {
                    if (isCancelled()) {
                        break;
                    }
                    if (actionIndex != -1 && operationStepSequencesIndex != -1) {
                        Object value = getValueAt(excelRow, operationStepSequencesIndex);
                        if (value != null) {
                            String operationStepSequencesStr = value.toString();
                            if (!StringUtils.isEmpty(operationStepSequencesStr)) {
                                StringBuilder modifiedStringBuilder = new StringBuilder();
                                String[] operationStepSequences = operationStepSequencesStr.split("\n");
                                for (int i = 0; i < operationStepSequences.length; i++) {
                                    if (operationStepSequences[i].contains("-check-")) {
                                        modifiedStringBuilder.append(operationStepSequences[i]);
                                    } else {
                                        String modifiedLine = operationStepSequences[i] + "-check-" + (i + 1);
                                        modifiedStringBuilder.append(modifiedLine);
                                    }
                                    if (i < operationStepSequences.length - 1) {
                                        modifiedStringBuilder.append("\n");
                                    }
                                }
                                setValueAt(modifiedStringBuilder.toString(), excelRow, operationStepSequencesIndex);
                            }
                        }
                    }
                }
                return null;
            }

            @Override
            protected void process(List<CellGroup> chunks) {

            }

            @Override
            protected void done() {
                repaint();
            }
        };
        worker.execute();
    }

    private void showRowHeightDialog() {
        RowHeightDialog rowHeightDialog = new RowHeightDialog(this);
        rowHeightDialog.setModal(true);//确保弹出的窗口在其他窗口前面
        rowHeightDialog.setVisible(true);
    }

    private void paddingContentToColumns() {
        int row = rowAtPoint(mouseClickPoint);
        int col = columnAtPoint(mouseClickPoint);
        List<Integer> excelRowList = getCheckedRows();
        if (excelRowList.isEmpty()) {
            SwingUtil.showWarningDialog(null, "请先选中需要填充内容的用例");
            return;
        }
        Object content = getValueAt(row, col);
        for (Integer integer : excelRowList) {
            setValueAt(content, integer, col);
        }
        repaint();
    }

    private void fireTableCellUpdated(int row, int column) {
        ((AbstractTableModel) getModel()).fireTableCellUpdated(row, column);
    }

    @Override
    public void dragRowsCompleted(Map<Integer, Integer> map) {
        swapTableList(map);
    }

    @Override
    public void selectedTask(TaskFile taskFile) {
        String excelTableName = taskFile.getExcelTableName();
        List<Integer> selectedRows = taskFile.getSelectedRows();
        if (excelTableName.equals(this.sheetName)) {
            for (Integer selectedRow : selectedRows) {
                setRowSelected(selectedRow, true);
            }
        }

    }

    @Override
    public void unselectedTask(TaskFile taskFile) {
        String excelTableName = taskFile.getExcelTableName();
        List<Integer> selectedRows = taskFile.getSelectedRows();
        if (excelTableName.equals(this.sheetName)) {
            for (Integer selectedRow : selectedRows) {
                setRowSelected(selectedRow, false);
            }
        }
    }

    @Data
    @AllArgsConstructor
    public static class RowGroup {
        int testRowIndex;
        int actuallyRowIndex;
    }


    @Data
    @AllArgsConstructor
    public static class CellGroup {
        int row;
        int col;
    }

    private void singleRowConvertScript() {
        List<Integer> excelRowList = new ArrayList<>();
        int row = rowAtPoint(mouseClickPoint);
        if (row != -1) {
            excelRowList.add(row);
            convertScript(excelRowList);
        }
    }


    private void convertScript(List<Integer> excelRowList) {
        if (excelRowList.isEmpty()) {
            SwingUtil.showWarningDialog(null, "请先选择需要转化的案例");
            return;
        }

        // 如果已经有转换任务在运行，不允许启动新的转换
        if (convertTaskWorker != null && !convertTaskWorker.isDone()) {
            SwingUtil.showWarningDialog(null, "已有转换任务正在进行，请等待完成");
            return;
        }

        SmartSequenceConvertDialog dialog = SmartSequenceConvertDialog.getInstance(mainModel);
        dialog.showDialog();
        if (!dialog.isConfirmed()) {
            return;
        }

        // 禁用脚本转换菜单项
        updateScriptConversionMenuItems(false);

        int initialConditionIndex = findTableColumnIndexByName(ColumnNameConstants.getInstance().getInitialCondition());
        int actionIndex = findTableColumnIndexByName(ColumnNameConstants.getInstance().getAction());
        int expectedResultIndex = findTableColumnIndexByName(ColumnNameConstants.getInstance().getExpectedResult());
        int preconditionSequencesIndex = findTableColumnIndexByName(ColumnNameConstants.getInstance().getPreconditionSequences());
        int operationStepSequencesIndex = findTableColumnIndexByName(ColumnNameConstants.getInstance().getOperationStepSequences());
        int expectedResultSequencesIndex = findTableColumnIndexByName(ColumnNameConstants.getInstance().getExpectedResultSequences());
        ActionSequenceLlmScriptGenerator actionSequenceLlmScriptGenerator = ActionSequenceLlmScriptGenerator.getInstance(mainModel);
        long st = System.currentTimeMillis();
        // 使用 SwingWorker 进行后台处理
        convertTaskWorker = new NamedSwingWorker<Void, CellGroup>("ActionSequenceConvertTask") {
            final String ongoingHint = "正在转换中...";

            @Override
            protected Void performTask() throws Exception {
                progressBar.setMaximum(excelRowList.size());
                int currentIndex = 0;
                progressBar.setString(String.format("%d / %d", currentIndex, excelRowList.size()));
                if (dialog.isSelfHostModel()) {
                    actionSequenceLlmScriptGenerator.startGeneration(excelRowList.size());
                }
                for (Integer excelRow : excelRowList) {
                    // 新增暂停检查点
                    checkPaused();

                    if (isCancelled()) {
                        break;
                    }
                    mainModel.getTestCaseTableModel().outputStatusInfo(String.format("当前转化第%d行", excelRow + 1));
                    ActionSequenceSimpleContext actionSequenceContext = combineSimpleActionSequenceContext(excelRow);
                    actionSequenceContext.setConvertMethod(dialog.getConvertMethod());
                    actionSequenceContext.setLlmType(dialog.getLlmType());
                    List<String> actionSequenceList = new ArrayList<>();
                    //设置转换前的提示
                    setValueAt(ongoingHint, excelRow, preconditionSequencesIndex);
                    setValueAt(ongoingHint, excelRow, operationStepSequencesIndex);
                    setValueAt(ongoingHint, excelRow, expectedResultSequencesIndex);
                    if (dialog.isSelfHostModel()) {
                        //自研大模型
                        try {
                            //TODO：移动到后端
                            ActionSequenceRequest actionSequenceRequest = new ActionSequenceRequest(
                                    mainModel.getAppInfo().getBuName(),
                                    actionSequenceContext.getPrecondition(),
                                    actionSequenceContext.getOperationStep(),
                                    actionSequenceContext.getExpectResult(),
                                    UserPromptPanel.getInstance(mainModel).getPrompt(),
                                    SystemPrompt.getInstance().getPrompt());
                            ActionSequenceRequest responsePackage = actionSequenceLlmScriptGenerator.generateCombinedActionSequences(actionSequenceRequest).getActionSequenceRequest();
                            actionSequenceList = new ArrayList<>(Arrays.asList(responsePackage.getCondition(), responsePackage.getAction(), responsePackage.getExpect()));

                        } catch (LLMException e) {
                            setValueAt(e.getMessage(), excelRow, preconditionSequencesIndex);
                            setValueAt(e.getMessage(), excelRow, operationStepSequencesIndex);
                            setValueAt(e.getMessage(), excelRow, expectedResultSequencesIndex);
                        }
                    } else {
                        //南大模型
                        actionSequenceList = OperationTargetHolder.getActionSequenceKit().convertActionSequence(actionSequenceContext);
                    }
                    if (actionSequenceList.size() < 3) {
                        return null;
                    }
                    if (initialConditionIndex != -1 && preconditionSequencesIndex != -1) {
                        String initialCondition = actionSequenceList.get(0);
                        setValueAt(initialCondition, excelRow, preconditionSequencesIndex);
                        publish(new CellGroup(excelRow, preconditionSequencesIndex));
                    }
                    if (actionIndex != -1 && operationStepSequencesIndex != -1) {
                        String action = actionSequenceList.get(1);
                        setValueAt(action, excelRow, operationStepSequencesIndex);
                        publish(new CellGroup(excelRow, operationStepSequencesIndex));
                    }
                    if (expectedResultIndex != -1 && expectedResultSequencesIndex != -1) {
                        String expectedResult = actionSequenceList.get(2);
                        setValueAt(expectedResult, excelRow, expectedResultSequencesIndex);
                        publish(new CellGroup(excelRow, expectedResultSequencesIndex));
                    }
                    if (isCancelled()) { // 更新进度前检查
                        break;
                    }
                    ++currentIndex;
                    if (dialog.isSelfHostModel()) {
                        actionSequenceLlmScriptGenerator.updateProgress(currentIndex);
                    }
                    progressBar.setValue(currentIndex);
                    progressBar.setString(String.format("%d / %d", currentIndex, excelRowList.size()));
                    // 更新当前行高以适应新内容
                    updateRowHeight(excelRow);
                    updateFrozenHeight(excelRow);
                    saveExcelCaseRowDataToDB(excelRow);
                }
                return null;
            }

            @Override
            protected void process(List<CellGroup> chunks) {
                for (CellGroup cellGroup : chunks) {
                    ((AbstractTableModel) getModel()).fireTableRowsUpdated(cellGroup.getRow(), cellGroup.getCol());
                }
            }


            @Override
            protected void done() {
                boolean interrupted = isCancelled();
                try {
                    get();
                    String msg = String.format("案例转换已完成，耗时:%.2fs", (System.currentTimeMillis() - st) / 1000.0);
                    log.info(msg);
                    mainModel.getTestCaseTableModel().outputStatusInfo(msg);
                    interrupted = false;
                } catch (CancellationException ignored) {
                    interrupted = true;
                    String msg = "案例转换已取消";
                    log.warn(msg);
                    mainModel.getTestCaseTableModel().outputStatusInfo(msg);
                } catch (ExecutionException e) {
                    interrupted = true;
                    // 获取原始异常
                    Throwable cause = e.getCause();
                    if (cause instanceof ConnectException) {
                        SwingUtil.showWarningDialog(null, "大模型脚本生成服务无法访问，请联系开发者解决");
                        return;
                    }
                    log.error(ExceptionUtils.getStackTrace(cause));  // Apache Commons Lang提供
                    SwingUtil.showWarningDialog(null, ExceptionUtils.getMessage(cause));
                } catch (Exception e) {
                    interrupted = true;
                    log.error(e.getMessage(), e);
                    SwingUtil.showWarningDialog(null, e.getMessage());
                } finally {
                    // 如果需要LLM操作，先禁用相关按钮，等操作完成后再更新UI
                    if (dialog.isSelfHostModel()) {
                        final boolean finalInterrupted = interrupted;

                        // 立即禁用相关按钮，防止用户在LLM操作期间点击
                        SwingUtilities.invokeLater(() -> {
                            stopProgressButton.setEnabled(false);
                            pauseResumeButton.setEnabled(false);
                            progressBar.setString(finalInterrupted ? "正在中断LLM会话..." : "正在停止LLM会话...");
                        });

                        // 使用后台线程处理LLM操作，完成后更新UI
                        CompletableFuture.runAsync(() -> {
                            try {
                                if (finalInterrupted) {
                                    log.info("开始中断LLM脚本生成...");
                                    actionSequenceLlmScriptGenerator.interruptGenerateScript();
                                    log.info("LLM脚本生成中断完成");
                                } else {
                                    log.info("开始停止LLM脚本生成...");
                                    actionSequenceLlmScriptGenerator.stopGenerateScript();
                                    log.info("LLM脚本生成停止完成");
                                }
                            } catch (IOException e) {
                                log.error("LLM操作失败: {}", e.getMessage(), e);
                            } catch (Exception e) {
                                log.error("LLM操作发生未知异常: {}", e.getMessage(), e);
                            }
                        }, Executors.newSingleThreadExecutor(r -> {
                            Thread t = new Thread(r, "LLMCleanupTask-" + System.currentTimeMillis());
                            t.setDaemon(true);
                            return t;
                        })).whenComplete((result, throwable) -> {
                            // LLM操作完成后，在UI线程中更新界面状态
                            SwingUtilities.invokeLater(() -> {
                                if (throwable != null) {
                                    log.error("LLM清理任务执行失败", throwable);
                                    progressBar.setString("LLM会话清理失败");
                                } else {
                                    progressBar.setString(finalInterrupted ? "LLM会话已中断" : "LLM会话已停止");
                                }

                                // 短暂显示状态信息后清理UI
                                Timer timer = new Timer(1500, e -> {
                                    progressBar.setValue(0);
                                    progressBar.setVisible(false);
                                    stopProgressButton.setVisible(false);
                                    pauseResumeButton.setVisible(false);
                                    stopProgressButton.setEnabled(true);
                                    pauseResumeButton.setEnabled(true);
                                    convertTaskWorker = null;

                                    // 重新启用脚本转换菜单项
                                    updateScriptConversionMenuItems(true);

                                    repaint();
                                });
                                timer.setRepeats(false);
                                timer.start();
                            });
                        });
                    } else {
                        // 如果不是自研模型，直接更新UI
                        progressBar.setValue(0);
                        progressBar.setVisible(false);
                        stopProgressButton.setVisible(false);
                        pauseResumeButton.setVisible(false);
                        convertTaskWorker = null;
                        repaint();
                    }
                }
            }
        };
        convertTaskWorker.execute();
        if (progressBar == null) {
            progressBar = excelCaseTabPaneView.getProgressBar();
        }
        progressBar.setVisible(true);
        if (stopProgressButton == null) {
            stopProgressButton = excelCaseTabPaneView.getStopProgressButton();
        }
        stopProgressButton.setVisible(true);
        if (pauseResumeButton == null) {
            pauseResumeButton = excelCaseTabPaneView.getPauseResumeButton();
        }
        pauseResumeButton.setVisible(true);
    }

    private void checkPaused() throws InterruptedException {
        synchronized (pauseLock) {
            while (excelCaseTabPaneView.isPaused()) {
                try {
                    // 添加超时机制，避免无限等待
                    pauseLock.wait(1000); // 等待1秒后重新检查

                    // 检查线程是否被中断
                    if (Thread.currentThread().isInterrupted()) {
                        Thread.currentThread().interrupt(); // 恢复中断状态
                        throw new InterruptedException("Thread was interrupted during pause");
                    }

                    // 检查convertTaskWorker是否被取消
                    if (convertTaskWorker != null && convertTaskWorker.isCancelled()) {
                        break; // 如果任务被取消，跳出等待循环
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.debug("checkPaused方法被中断");
                    throw e; // 重新抛出中断异常
                }
            }
        }
    }

    //FIXME:
    private void scriptReOrdering() {
        //没有UUID data 局部Map未看见使用 屏蔽
        //HashMap<String, HashMap<String, String>> dataMap = new HashMap<>();
        int preconditionSequencesIndex = findTableColumnIndexByName(ColumnNameConstants.getInstance().getPreconditionSequences());
        int operationStepSequencesIndex = findTableColumnIndexByName(ColumnNameConstants.getInstance().getOperationStepSequences());
        int expectedResultSequencesIndex = findTableColumnIndexByName(ColumnNameConstants.getInstance().getExpectedResultSequences());
        List<Integer> rowCount = getCheckedRows();
        for (Integer row : rowCount) {
            //String uuid = ObjectUtils.defaultIfNull(getModel().getValueAt(row, SwingUtil.getColumnIndex(this, UUID_KEY)), "").toString();
            String preconditionSequences = reorderLinesWithNewNumbers(ObjectUtils.defaultIfNull(getModel().getValueAt(row, preconditionSequencesIndex), "").toString());
            String operationStepSequences = reorderLinesWithNewNumbers(ObjectUtils.defaultIfNull(getModel().getValueAt(row, operationStepSequencesIndex), "").toString());
            String expectedResultSequences = reorderLinesWithNewNumbers(ObjectUtils.defaultIfNull(getModel().getValueAt(row, expectedResultSequencesIndex), "").toString());
            setValueAt(preconditionSequences, row, preconditionSequencesIndex);
            setValueAt(operationStepSequences, row, operationStepSequencesIndex);
            setValueAt(expectedResultSequences, row, expectedResultSequencesIndex);
            /*HashMap<String, String> contentMap = new HashMap<>();
            contentMap.put("preconditionSequences", preconditionSequences);
            contentMap.put("operationStepSequences", operationStepSequences);
            contentMap.put("expectedResultSequences", expectedResultSequences);
            dataMap.put(uuid, contentMap);*/
        }
        repaint();
//        JsonResponse<List<ExcelCaseModel>> res = ClientManager.getExcelManager().findExcelCaseByTableName(sheetName);
//        if (res.isOk()) {
//            List<ExcelCaseModel> excelCaseModelList = res.getData();
//            for (ExcelCaseModel excelCaseModel : excelCaseModelList) {
//                String uuid = excelCaseModel.getUuid();
//                if (dataMap.containsKey(uuid)) {
//                    HashMap<String, String> valueMap = dataMap.get(uuid);
//                    excelCaseModel.setInitTestSequences(valueMap.get("preconditionSequences"));
//                    excelCaseModel.setActionTestSequences(valueMap.get("operationStepSequences"));
//                    excelCaseModel.setExpectedTestSequences(valueMap.get("expectedResultSequences"));
//                }
//            }
//            ClientManager.getExcelManager().updateExcelCase(excelCaseModelList);
//        }

        //FIXMe:增加修改用例
    }


    private void showColumnWidthDialog() {
        ColumnWidthDialog columnWidthDialog = new ColumnWidthDialog(this);
        columnWidthDialog.setModal(true);//确保弹出的窗口在其他窗口前面
        columnWidthDialog.setVisible(true);
    }

    @Override
    public void setColumnWidth(int width) {
        TableColumn column = getColumnModel().getColumn(columnAtPoint(mouseClickPoint));
        column.setPreferredWidth(width);
    }

    public void setRowHeight(List<Integer> rowList, int width) {
        for (Integer row : rowList) {
            setRowHeight(row, width);
            getRowHeaderTable().syncRowHeight(row);
        }
        repaint();
    }

    public int getClickRow() {
        return rowAtPoint(mouseClickPoint);
    }


    @Override
    protected void createMenu() {
        super.createMenu();
        addExcelCaseTableMenu();
        addLineWrapStroke();
    }

    public synchronized void createTestStep(int row) {
        if (rendering) {
            return;
        }
        createTestStep(row, -1);
    }

    public synchronized void createTestStep(int row, int column) {
        if (row == -1) {
            return;
        }
        List<TestStep> testStepList;
        //FIXME:优化getColum方法
//        if (column == SwingUtil.getColumnIndex(this, ColumnNameConstants.getInstance().getPreconditionSequences())) {
//            testStepList = convertSingleTestStepData(row, column, ColumnNameConstants.getInstance().getPreconditionSequences());
//        } else if (column == SwingUtil.getColumnIndex(this, ColumnNameConstants.getInstance().getOperationStepSequences())) {
//            testStepList = convertSingleTestStepData(row, column, ColumnNameConstants.getInstance().getOperationStepSequences());
//        } else if (column == SwingUtil.getColumnIndex(this, ColumnNameConstants.getInstance().getExpectedResultSequences())) {
//            testStepList = convertSingleTestStepData(row, column, ColumnNameConstants.getInstance().getExpectedResultSequences());
//        } else {
//            testStepList = convertAllTestStepData(row);
//        }
        String columnName = getColumnName(column);
        if (!ArrayUtil.isEmpty(columnName)) {
            ActionSequenceCheckReporter actionSequenceCheckReporter = actionSequenceCheckReporterManager.get(row);
            mainModel.getTestStepModel().setExcelTableRowId(row);
            mainModel.getTestStepModel().groupingByStepType(convertAllTestStepData(row));
            mainModel.getTestStepModel().switchTestStep(columnName);
            if (actionSequenceCheckReporter == null || actionSequenceCheckReporter.isCheckOk()) {
                mainModel.getTestStepModel().clearTestStepErrorColor();
                mainModel.getTestStepModel().clearTestStepExecuteColor();
            } else {
                mainModel.getTestStepModel().renderTestStepCheckReport(actionSequenceCheckReporter);
            }
        }
    }

    private void changeTestStepBackgroundColor(String stepType) {
        mainModel.getTestStepModel().renderTestStepRowHeaderMarker(stepType);
    }

    @Override
    protected Object[] convertData(ExcelRow excelRow) {
        List<Object> array = new ArrayList<>(excelRow.getData());
        array.add(0, excelRow.isSelected());
        return array.toArray();
    }

    protected List<TestStep> convertSingleTestStepData(int row, int column, String type) {
        List<TestStep> allTestStepList = new ArrayList<>();
        try {
            Object sequencesObject = getValueAt(row, column);
            List<TestStep> testStepList = createTestStepByType(sequencesObject, type);
            allTestStepList.addAll(testStepList);
        } catch (Exception e) {
            return null;
        }
        return allTestStepList;
    }


    /**
     * 转换成测试序列步骤列表
     *
     * @param row 输入行
     * @return 测试序列步骤列表
     */
    protected List<TestStep> convertAllTestStepData(int row) {
        List<TestStep> allTestStepList = new ArrayList<>();
        try {
            Object initTestSequences = getValueAt(row, SwingUtil.getColumnIndex(this, ColumnNameConstants.getInstance().getPreconditionSequences()));
            Object actionTestSequences = getValueAt(row, SwingUtil.getColumnIndex(this, ColumnNameConstants.getInstance().getOperationStepSequences()));
            Object expectedTestSequences = getValueAt(row, SwingUtil.getColumnIndex(this, ColumnNameConstants.getInstance().getExpectedResultSequences()));
            List<TestStep> initTestStepList = createTestStepByType(initTestSequences, ColumnNameConstants.getInstance().getPreconditionSequences());
            List<TestStep> actionTestStepList = createTestStepByType(actionTestSequences, ColumnNameConstants.getInstance().getOperationStepSequences());
            List<TestStep> expectedTestStepList = createTestStepByType(expectedTestSequences, ColumnNameConstants.getInstance().getExpectedResultSequences());
            allTestStepList.addAll(initTestStepList);
            allTestStepList.addAll(actionTestStepList);
            allTestStepList.addAll(expectedTestStepList);
        } catch (Exception e) {
            return null;
        }
        return allTestStepList;
    }


    private List<TestStep> createTestStepByType(Object testSequences, String type) {
        List<TestStep> testStepList = new ArrayList<>();
        String testSequenceString = (testSequences == null ? "" : testSequences + "\n");
        List<String> initTestList = splitCellData(testSequenceString);
        for (String step : initTestList) {
            TestStep testStep = new TestStep();
            testStep.setTestStep(step);
            testStep.setType(type);
            testStepList.add(testStep);
        }
        return testStepList;
    }


    @Override
    protected void setPreferredColumn() {
//        fitTableColumnWidth();
    }

    @Override
    protected void setTableHeaderConfig() {
        getTableHeader().setReorderingAllowed(false);
    }

    @Override
    protected void setTableFont() {
        setFont(new Font("微软雅黑", Font.BOLD, 12));
        getTableHeader().setFont(new Font("微软雅黑", Font.BOLD, 12));
    }

    @Override
    protected void changeColumnWidth() {
        super.changeColumnWidth();
        TableColumnModel columnModel = getColumnModel();
        int columnCount = columnModel.getColumnCount();
        Map<String, Integer> columnWidths = new HashMap<>();
        // 获取调整后的列宽
        for (int i = 0; i < columnCount; i++) {
            TableColumn column = columnModel.getColumn(i);
            columnWidths.put((String) column.getHeaderValue(), column.getWidth());
        }
        CaseContent caseContent = CaseConfigJson.getInstance().getCaseContentBySheetName(sheetName);
        if (caseContent == null)
            return;
        List<CaseHeaderContent> caseHeaderContentList = caseContent.getCaseHeaderContentList();
        for (CaseHeaderContent caseHeaderContent : caseHeaderContentList) {
            Integer columnWidth = columnWidths.get(caseHeaderContent.getColumnName());
            if (columnWidth != null) {
                caseHeaderContent.setColumnWidth(columnWidth);
            }
        }
        CaseConfigJsonManager.syncExcelCaseConfigFile();
    }


    /**
     * 渲染表格数据
     *
     * @param excelCaseRenderTabbedPane
     * @param excelSheetData            表格数据
     * @param manualImport
     * @param entry
     * @param sheetName
     */
    public void renderCase(ExcelCaseRenderTabbedPane excelCaseRenderTabbedPane, ExcelSheetData excelSheetData, boolean manualImport, Map.Entry<String, ExcelSheetData> entry, String sheetName) {
        log.info("renderCase sheet:{}", sheetName);
        List<String> tableHeader = excelSheetData.getTableHeader();
        tableHeader.add(0, SELECT_ALL_TEXT); // 添加全选列
        List<HashMap<Integer, String>> tableData = excelSheetData.getTableData();
        ((DefaultTableModel) getModel()).setDataVector(new Object[0][tableHeader.size()], tableHeader.toArray(new String[0]));
        getTableHeader().getColumnModel().getColumn(0).setMaxWidth(50);
        setEnabled(false);
        rendering = true;
        renderRows(tableData);
        mainModel.getTestCaseTableModel().testCaseLoaded();
        // 计算用例数量
//        int caseCount = excelSheetData.getTableData().size();
//        String displayName = sheetName + "(" + caseCount + ")";
        excelCaseRenderTabbedPane.syncTableConfig(this, manualImport, entry, sheetName);
        rendering = false;
        setEnabled(true);
    }


    //确定列名
    public void syncExcelCaseTemplateConfig(List<String> tableHeader) {
        ColumnNameConstants columnNameConstants = ColumnNameConstants.getInstance();
        columnNameConstants.setNo(findCommonElement(tableHeader,
                ExcelCaseTemplate.getInstance().getTemplateColumnMap().get("no")));
        columnNameConstants.setTestCaseID(findCommonElement(tableHeader,
                ExcelCaseTemplate.getInstance().getTemplateColumnMap().get("testCaseID")));
        columnNameConstants.setTestKey(findCommonElement(tableHeader,
                ExcelCaseTemplate.getInstance().getTemplateColumnMap().get("testKey")));
        columnNameConstants.setInitialCondition(findCommonElement(tableHeader,
                ExcelCaseTemplate.getInstance().getTemplateColumnMap().get("initialCondition")));
        columnNameConstants.setAction(findCommonElement(tableHeader,
                ExcelCaseTemplate.getInstance().getTemplateColumnMap().get("action")));
        columnNameConstants.setExpectedResult(findCommonElement(tableHeader,
                ExcelCaseTemplate.getInstance().getTemplateColumnMap().get("expectedResult")));
        columnNameConstants.setPreconditionSequences(findCommonElement(tableHeader,
                ExcelCaseTemplate.getInstance().getTemplateColumnMap().get("initTestSequences")));
        columnNameConstants.setOperationStepSequences(findCommonElement(tableHeader,
                ExcelCaseTemplate.getInstance().getTemplateColumnMap().get("actionTestSequences")));
        columnNameConstants.setExpectedResultSequences(findCommonElement(tableHeader,
                ExcelCaseTemplate.getInstance().getTemplateColumnMap().get("expectedTestSequences")));
        columnNameConstants.setChoose(findCommonElement(tableHeader,
                ExcelCaseTemplate.getInstance().getTemplateColumnMap().get("choose")));
        columnNameConstants.setTestResult(findCommonElement(tableHeader,
                ExcelCaseTemplate.getInstance().getTemplateColumnMap().get("testResult")));
        columnNameConstants.setActualResult(findCommonElement(tableHeader,
                ExcelCaseTemplate.getInstance().getTemplateColumnMap().get("actualResult")));
        columnNameConstants.setTester(findCommonElement(tableHeader,
                ExcelCaseTemplate.getInstance().getTemplateColumnMap().get("tester")));
        columnNameConstants.setTestTime(findCommonElement(tableHeader,
                ExcelCaseTemplate.getInstance().getTemplateColumnMap().get("testTime")));
        columnNameConstants.setRemark(findCommonElement(tableHeader,
                ExcelCaseTemplate.getInstance().getTemplateColumnMap().get("remark")));
        columnNameConstants.setTestScenario(findCommonElement(tableHeader,
                ExcelCaseTemplate.getInstance().getTemplateColumnMap().get("testScenario")));
        columnNameConstants.setTestLog(findCommonElement(tableHeader,
                ExcelCaseTemplate.getInstance().getTemplateColumnMap().get("testLog")));
        columnNameConstants.setTargetTestTimes(null);
        columnNameConstants.setTestedTimes(null);
        columnNameConstants.setTestedPassTime(null);
        OperationTargetHolder.getExcelKit().syncExcelColumnNames(columnNameConstants);
    }


    public void checkActionSequenceGrammar(Integer row) {
        checkActionSequenceGrammar(row, null);
    }

    /**
     * 动作序列语法检查
     *
     * @param row 行
     */
    public void checkActionSequenceGrammar(Integer row, Integer column) {
        ActionSequenceContext actionSequenceContext = combineActionSequenceContext(row);
        ActionSequenceCheckReporter actionSequenceCheckReporter = checkActionSequenceGrammar(actionSequenceContext);
        printActionSequenceReport(String.format("第%d行", row + 1), actionSequenceCheckReporter, true);
        renderActionSequenceGrammarCheckResult(row, column, actionSequenceCheckReporter, true);
    }

    /**
     * 渲染动作序列语法检查结果
     *
     * @param actionSequenceCheckReporter 动作序列语法检查结果
     */
    public void renderActionSequenceGrammarCheckResult(Integer row, Integer column,
                                                       ActionSequenceCheckReporter actionSequenceCheckReporter, boolean onlyCheck) {
        //TODO:降低SwingUtil.invokeLater颗粒度
        SwingUtil.invokeLater(() -> mainModel.getTestStepModel().renderTestStepCheckReport(actionSequenceCheckReporter));
        actionSequenceCheckReporterManager.put(row, actionSequenceCheckReporter);
        if (column != null) {
            int preconditionSequencesIndex = findTableColumnIndexByName(ColumnNameConstants.getInstance().getPreconditionSequences());
            int operationStepSequencesIndex = findTableColumnIndexByName(ColumnNameConstants.getInstance().getOperationStepSequences());
            int expectedResultSequencesIndex = findTableColumnIndexByName(ColumnNameConstants.getInstance().getExpectedResultSequences());
            updateColorForColumn(row, column, preconditionSequencesIndex, actionSequenceCheckReporter, "precondition", onlyCheck);
            updateColorForColumn(row, column, operationStepSequencesIndex, actionSequenceCheckReporter, "operationStep", onlyCheck);
            updateColorForColumn(row, column, expectedResultSequencesIndex, actionSequenceCheckReporter, "expectedResult", onlyCheck);
        } else {
            boolean checkPass = actionSequenceCheckReporter.isCheckOk();
            boolean executePass = actionSequenceCheckReporter.isExecuteOk();
            SwingUtil.invokeLater(() -> {
                setColor(row, null,
                        checkPass ? onlyCheck ? CHECK_PASS_COLOR : executePass ? TEST_PASS_COLOR : TEST_FAIL_COLOR :
                                CEHCK_FAIL_COLOR);
                repaint();
            });
        }

    }

    private void updateColorForColumn(Integer row, Integer column, int columnIndex,
                                      ActionSequenceCheckReporter reporter, String checkType, boolean onlyCheck) {
        if (columnIndex != -1 && column == columnIndex && reporter != null) {
            ActionSequenceCheckResult checkResult = getCheckResultByType(reporter, checkType);
            if (checkResult != null) {
                boolean checkPass = checkResult.isCheckOk();
                boolean executePass = checkResult.isExecuteOk();
                SwingUtil.invokeLater(() -> {
                    setColor(row, columnIndex,
                            checkPass ? onlyCheck ? CHECK_PASS_COLOR : executePass ? TEST_PASS_COLOR : TEST_FAIL_COLOR :
                                    CEHCK_FAIL_COLOR);
                    repaint();
                });
            }
        } else {
            ActionSequenceCheckResult checkResult = getCheckResultByType(reporter, checkType);
            if (checkResult != null) {
                boolean checkPass = checkResult.isCheckOk();
                SwingUtil.invokeLater(() -> {
                    setColor(row, columnIndex,
                            checkPass ? null : CEHCK_FAIL_COLOR);
                    repaint();
                });
            }
        }
    }

    private ActionSequenceCheckResult getCheckResultByType(ActionSequenceCheckReporter reporter, String type) {
        if ("precondition".equals(type)) {
            return reporter.getPreconditionCheckResult();
        } else if ("operationStep".equals(type)) {
            return reporter.getOperationStepCheckResult();
        } else if ("expectedResult".equals(type)) {
            return reporter.getExpectResultCheckResult();
        }
        return null;
    }

    /**
     * 渲染表格行 - 优化版本，支持分批加载
     *
     * @param hashMapList 映射数据
     */
    public void renderRows(List<HashMap<Integer, String>> hashMapList) {
        try {
            if (hashMapList.isEmpty()) {
                return;
            }
            setEnabled(false);
            setRendering(true);

            // 记录渲染开始
            ExcelTablePerformanceMonitor.getInstance().recordRenderStart(getSheetName(), hashMapList.size());

            // 使用分批渲染优化性能
            renderRowsBatch(hashMapList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            setRendering(false);
            setEnabled(true);
        }
    }

    /**
     * 分批渲染表格行，优化大数据量性能
     *
     * @param hashMapList 映射数据
     */
    private void renderRowsBatch(List<HashMap<Integer, String>> hashMapList) {
        final int totalRows = hashMapList.size();
        final int BATCH_SIZE = ExcelTableRenderConfig.calculateOptimalBatchSize(totalRows);
        final long availableMemory = Runtime.getRuntime().freeMemory() / (1024 * 1024); // MB
        final int batchInterval = ExcelTableRenderConfig.calculateBatchInterval(availableMemory);

        log.info("开始分批渲染表格，总行数: {}, 批次大小: {}, 间隔: {}ms", totalRows, BATCH_SIZE, batchInterval);

        // 先预设表格模型大小，避免频繁扩容
        DefaultTableModel model = (DefaultTableModel) getModel();
        model.setRowCount(totalRows);

        // 初始化所有ExcelRow对象到tableList
        getTableList().clear();
        for (int i = 0; i < totalRows; i++) {
            HashMap<Integer, String> hashMap = hashMapList.get(i);
            List<String> rowData = new ArrayList<>(hashMap.values());
            ExcelRow excelRow = new ExcelRow();
            excelRow.setSelected(false);
            excelRow.setData(rowData);
            excelRow.setRowNumber(i);
            getTableList().add(excelRow);
        }

        // 使用SwingWorker进行后台渲染
        SwingWorker<Void, Integer> renderWorker = new SwingWorker<Void, Integer>() {
            @Override
            protected Void doInBackground() throws Exception {
                for (int startIndex = 0; startIndex < totalRows; startIndex += BATCH_SIZE) {
                    if (isCancelled()) {
                        break;
                    }

                    int endIndex = Math.min(startIndex + BATCH_SIZE, totalRows);

                    // 在后台线程中准备数据
                    List<Object[]> batchData = new ArrayList<>();
                    for (int i = startIndex; i < endIndex; i++) {
                        ExcelRow excelRow = getTableList().get(i);
                        Object[] rowData = convertData(excelRow);
                        batchData.add(rowData);
                    }

                    // 在EDT中更新UI
                    final int finalStartIndex = startIndex;
                    final int finalEndIndex = endIndex;
                    final long batchStartTime = System.currentTimeMillis();

                    SwingUtilities.invokeLater(() -> {
                        try {
                            for (int i = 0; i < batchData.size(); i++) {
                                int rowIndex = finalStartIndex + i;
                                Object[] rowData = batchData.get(i);

                                // 直接设置数据到模型，避免触发事件
                                for (int col = 0; col < rowData.length && col < model.getColumnCount(); col++) {
                                    model.setValueAt(rowData[col], rowIndex, col);
                                }
                            }

                            // 批量通知模型更新
                            model.fireTableRowsUpdated(finalStartIndex, finalEndIndex - 1);

                            // 记录批处理性能
                            long batchDuration = System.currentTimeMillis() - batchStartTime;
                            ExcelTablePerformanceMonitor.getInstance().recordBatchProcessed(
                                    finalEndIndex - finalStartIndex, batchDuration);

                            // 发布进度
                            publish(finalEndIndex);
                        } catch (Exception e) {
                            log.error("批量渲染第{}到{}行时出错: {}", finalStartIndex, finalEndIndex, e.getMessage(), e);
                        }
                    });

                    // 短暂休眠，让UI有机会响应
                    Thread.sleep(batchInterval);
                }
                return null;
            }

            @Override
            protected void process(List<Integer> chunks) {
                if (!chunks.isEmpty()) {
                    int lastProcessed = chunks.get(chunks.size() - 1);
                    log.debug("已渲染行数: {}/{}", lastProcessed, totalRows);
                }
            }

            @Override
            protected void done() {
                try {
                    get(); // 检查是否有异常
                    log.info("表格渲染完成，总行数: {}", totalRows);

                    // 记录渲染完成
                    ExcelTablePerformanceMonitor.getInstance().recordRenderComplete(getSheetName());

                    // 延迟计算行高，只计算可见区域
                    SwingUtilities.invokeLater(() -> {
                        updateVisibleRowHeights();
                        setRendering(false);
                        setEnabled(true);
                        afterDataLoaded();

                        // 记录内存使用情况
                        ExcelTablePerformanceMonitor.getInstance().recordMemoryUsage();

                        // 打印性能报告
                        ExcelTablePerformanceMonitor.getInstance().logPerformanceReport();
                    });
                } catch (Exception e) {
                    log.error("表格渲染过程中发生错误: {}", e.getMessage(), e);
                    setRendering(false);
                    setEnabled(true);
                }
            }
        };

        renderWorker.execute();
    }

    /**
     * 只更新可见区域的行高，优化性能
     */
    private void updateVisibleRowHeights() {
        if (getParent() instanceof JViewport) {
            JViewport viewport = (JViewport) getParent();
            Rectangle visibleRect = viewport.getViewRect();

            int firstVisibleRow = rowAtPoint(new Point(0, visibleRect.y));
            int lastVisibleRow = rowAtPoint(new Point(0, visibleRect.y + visibleRect.height));

            if (firstVisibleRow == -1) firstVisibleRow = 0;
            if (lastVisibleRow == -1) lastVisibleRow = getRowCount() - 1;

            // 扩展可见区域，提前计算一些行高以优化滚动体验
            int extension = ExcelTableRenderConfig.VISIBLE_AREA_EXTENSION;
            int startRow = Math.max(0, firstVisibleRow - extension);
            int endRow = Math.min(getRowCount() - 1, lastVisibleRow + extension);

            // 只计算扩展后可见区域的行高
            for (int row = startRow; row <= endRow; row++) {
                updateRowHeightOptimized(row);
            }

            log.debug("更新可见行高: {} 到 {} (扩展后: {} 到 {})", firstVisibleRow, lastVisibleRow, startRow, endRow);
        }
    }

    /**
     * 优化的行高更新方法，使用缓存避免重复计算
     */
    private final Map<Integer, Integer> rowHeightCache = new ConcurrentHashMap<>();

    private void updateRowHeightOptimized(int row) {
        // 检查缓存
        Integer cachedHeight = rowHeightCache.get(row);
        if (cachedHeight != null) {
            // 缓存命中
            ExcelTablePerformanceMonitor.getInstance().recordRowHeightCalculation(true);
            setRowHeight(row, cachedHeight);
            return;
        }

        // 缓存未命中
        ExcelTablePerformanceMonitor.getInstance().recordRowHeightCalculation(false);

        // 计算行高
        int rowHeight = getRowHeight();
        for (int column = 0; column < getColumnCount(); column++) {
            try {
                Component comp = prepareRenderer(getCellRenderer(row, column), row, column);
                if (comp != null) {
                    rowHeight = Math.max(rowHeight, comp.getPreferredSize().height);
                }
            } catch (Exception e) {
                // 忽略单个单元格的渲染错误
                log.debug("计算行{}列{}高度时出错: {}", row, column, e.getMessage());
            }
        }

        // 缓存结果，但要控制缓存大小
        if (rowHeightCache.size() >= ExcelTableRenderConfig.ROW_HEIGHT_CACHE_MAX_SIZE) {
            // 清理部分缓存，保留最近使用的
            clearOldCacheEntries();
        }
        rowHeightCache.put(row, rowHeight);
        setRowHeight(row, rowHeight);
    }

    /**
     * 清理旧的缓存条目，保留最近的一半
     */
    private void clearOldCacheEntries() {
        if (rowHeightCache.size() > ExcelTableRenderConfig.ROW_HEIGHT_CACHE_MAX_SIZE / 2) {
            // 简单的LRU策略：清理一半缓存
            Set<Integer> keysToRemove = rowHeightCache.keySet().stream()
                    .limit(rowHeightCache.size() / 2)
                    .collect(Collectors.toSet());
            keysToRemove.forEach(rowHeightCache::remove);
            log.debug("清理行高缓存，移除{}个条目", keysToRemove.size());
        }
    }

    public void setFrozenTable(FrozenColumnTable tableView) {
        this.frozenColumnTable = tableView;
    }

    /**
     * 设置滚动面板，添加滚动监听器以优化行高计算
     */
    @Override
    public void setScrollPane(JScrollPane scrollPane) {
        super.setScrollPane(scrollPane);

        // 添加滚动监听器，在滚动时动态计算可见行高
        if (scrollPane != null && scrollPane.getViewport() != null) {
            scrollPane.getViewport().addChangeListener(e -> {
                // 延迟执行，避免滚动过程中频繁计算
                Timer timer = new Timer(ExcelTableRenderConfig.SCROLL_LISTENER_DELAY_MS, event -> {
                    if (!isRendering()) {
                        updateVisibleRowHeights();
                    }
                });
                timer.setRepeats(false);
                timer.start();
            });
        }
    }

    /**
     * 清除行高缓存，在数据变化时调用
     */
    public void clearRowHeightCache() {
        if (rowHeightCache != null) {
            rowHeightCache.clear();
            log.debug("已清除所有行高缓存");
        }
    }

    /**
     * 清除指定行的行高缓存
     */
    public void clearRowHeightCache(int row) {
        if (rowHeightCache != null) {
            rowHeightCache.remove(row);
        }
    }

    /**
     * 获取缓存统计信息
     */
    public String getRowHeightCacheStats() {
        if (rowHeightCache == null) {
            return "缓存未初始化";
        }
        return String.format("行高缓存统计: 大小=%d, 最大容量=%d",
                rowHeightCache.size(), ExcelTableRenderConfig.ROW_HEIGHT_CACHE_MAX_SIZE);
    }




    public void setRowHeaderTableFilter(JTable headerTable) {
        headerTable.setRowSorter(sorter);
    }

    @Override
    public void setColor(Integer row, Integer column, Color color) {
        TableCellRenderer tableCellRenderer = getDefaultRenderer(Object.class);
        if (tableCellRenderer instanceof ColorCellRenderer) {
            ((ColorCellRenderer) tableCellRenderer).getColorRender().setColor(row, column, color);
        }
//        excelCaseRenderTabbedPane.setFrozenColor(row, color);
        frozenColumnTable.setFrozenColor(row, color);
    }

    @Override
    public void clearColor() {
        TableCellRenderer tableCellRenderer = getDefaultRenderer(Object.class);
        if (tableCellRenderer instanceof ColorCellRenderer) {
            ((ColorCellRenderer) tableCellRenderer).getColorRender().clearColor();
        }
//        excelCaseRenderTabbedPane.clearFrozenColor();
        frozenColumnTable.clearFrozenColor();
    }

    @Override
    public void clearColor(int row) {
        TableCellRenderer tableCellRenderer = getDefaultRenderer(Object.class);
        if (tableCellRenderer instanceof ColorCellRenderer) {
            ((ColorCellRenderer) tableCellRenderer).getColorRender().clearColor(row);
        }
//        excelCaseRenderTabbedPane.clearFrozenColor(row);
        frozenColumnTable.clearFrozenColor(row);
    }

//    public void manuallySelectRow(int row) {
//        setRowSelectionInterval(row, row); //选中第一行
//        mainModel.getTestCaseTableModel().outputCaseInfo(String.format("当前选择第%d行", row + 1));
//    }

    public void renderColumns(List<CaseHeaderContent> caseHeaderConfigList) {
        for (CaseHeaderContent headerContent : caseHeaderConfigList) {
            if (headerContent.getColumnIndex() != 0) {
                if (headerContent.isBuiltin() || !headerContent.isVisible()) {
                    hiddenColumn(headerContent.getColumnIndex());
                } else {
                    showColumn(headerContent.getColumnIndex(), headerContent.getColumnWidth());
                }
            }
        }
    }

    @Override
    public void selectAllEvent(boolean isSelectAll) {
        getTableList().forEach(excelRow -> excelRow.setSelected(isSelectAll));
        recordAllSelectedStatus(isSelectAll);
    }

    @Override
    public boolean isAllSelected() {
        return getTableList().stream().allMatch(ExcelRow::isSelected);
    }

    @Override
    public void setRowSelected(int row, boolean selected) {
        ExcelRow excelRow = getRow(row);
        excelRow.setSelected(selected);
        setValueAt(selected, row, 0);
        //增加行复选框点击记忆功能，每次点击都会写入到excelCaseConfig.json文件
        if (sync) {
            recordRowSelectedStatus(row, selected);
        }
        repaint();
    }


    private void recordRowSelectedStatus(int row, boolean selected) {
        CaseContent caseHeader = CaseConfigJsonManager.getCaseHeaderBySheetName(sheetName);
        if (caseHeader != null) {
            List<CaseRowContent> caseRowContentList = caseHeader.getCaseRowContentList();
            caseRowContentList.get(row).setSelected(selected);
        }
        CaseConfigJsonManager.syncExcelCaseConfigFile();
    }

    private void recordAllSelectedStatus(boolean isSelectAll) {
        CaseContent caseHeader = CaseConfigJsonManager.getCaseHeaderBySheetName(sheetName);
        if (caseHeader != null) {
            List<CaseRowContent> caseRowContentList = caseHeader.getCaseRowContentList();
            for (CaseRowContent caseRowContent : caseRowContentList) {
                caseRowContent.setSelected(isSelectAll);
            }
        }
        CaseConfigJsonManager.syncExcelCaseConfigFile();
    }


    @Override
    public void selectRow(int row, boolean selected) {
        ExcelRow excelRow = getRow(row);
        excelRow.setSelected(selected);
        setValueAt(selected, row, 0);
    }


    public List<ExcelRow> getSelectedRowsByCheckBox() {
        return getTableList().stream().filter(ExcelRow::isSelected).collect(Collectors.toList());
    }

    public void registerModelObservers() {
        mainModel.getTestStepModel().registerObserver(this);
        mainModel.getTestCaseTableModel().registerObserver(this);
        mainModel.getTaskModel().registerObserver(this);
    }

    public void updateTestStepScrollPane(int row) {
        mainModel.getTestStepModel().handleTestStepScrollPane(row);
    }

    @Override
    public void selectedRangeActivated() {
        showSelectedRangeDialog();
    }

    private void ctrlGShortcut() {
        Action locateRowAction = new AbstractAction() {
            @Override
            public void actionPerformed(ActionEvent e) {
                locatingRowDialog();
            }
        };
        InputMap inputMap = this.getInputMap(JComponent.WHEN_IN_FOCUSED_WINDOW);
        ActionMap actionMap = this.getActionMap();

        KeyStroke keyStroke = KeyStroke.getKeyStroke(KeyEvent.VK_G, InputEvent.CTRL_DOWN_MASK);
        String key = "locateRowAction";
        inputMap.put(keyStroke, key);
        actionMap.put(key, locateRowAction);
    }

    private static class locatingRowDialog extends SpinnerDialog<Integer> {
        public locatingRowDialog(String title, String message, int initValue) {
            super(title, message, initValue);
        }

        @Override
        public Integer getValue() {
            if (isValueValid()) {
                return getSpinnerValue();
            }
            return null;
        }
    }

    public void showProgressBar(int testCycle, int checkRows) {
        progressBar.setVisible(true);
        progressBar.setMaximum(checkRows);
        cycleTimes = testCycle;
        progressBar.setString(String.format("%d - %d / %d", testCycle, 0, checkRows));
    }

    public void progressBarValueSet(int rowIndex, int checkRows) {
        progressBar.setValue(rowIndex + 1);
        progressBar.setString(String.format("%d / %d (Loop %d)", rowIndex + 1, checkRows, cycleTimes));
    }

    public void stopProgressBar() {
        progressBar.setValue(0);
        progressBar.setVisible(false);
    }

    public void scrollToRow(int rowIndex) {
        if (isAutoScrollingPaused) {
            // 如果自动滚动被暂停，则不执行滚动
            return;
        }
        if (rowIndex >= 0 && rowIndex < this.getRowCount()) {
            Rectangle rect = this.getCellRect(rowIndex, 0, true);
            Component parent = this.getParent();
            while (parent != null && !(parent instanceof JScrollPane)) {
                parent = parent.getParent();
            }
            if (parent != null) {
                JScrollPane scrollPane = (JScrollPane) parent;
                JViewport viewport = scrollPane.getViewport();
                Point currentViewPosition = viewport.getViewPosition();
                // 仅调整纵向位置，保持横向位置不变
                // 计算目标行的底部 Y 坐标
                int targetBottomY = rect.y + rect.height;
                int viewportHeight = viewport.getHeight();
                // 如果目标行的底部 Y 坐标大于视口的底部 Y 坐标，则需要滚动
                if (targetBottomY > currentViewPosition.y + viewportHeight) {
                    // 将视口滚动到目标行的底部与视口底部对齐
                    currentViewPosition.y = targetBottomY - viewportHeight;
                    viewport.setViewPosition(currentViewPosition);
                }
            }
        }
    }

    public void locatingRowDialog() {
        SpinnerDialog<Integer> dialog = new locatingRowDialog("定位到设置行对话框", "定位行", 1);
        Integer location = dialog.getValue();
        if (location != null) {
            List<ExcelRow> excelRows = getAllRowsData();
            if (!excelRows.isEmpty()) {
                SwingUtilities.invokeLater(() -> {
                    int rowIndex = location - 1;
                    scrollToRow(rowIndex);
                });
            }
        }
    }

    private void showSelectedRangeDialog() {
        SelectTestRangeDialog selectTestRangeDialog = new SelectTestRangeDialog(this);
        selectTestRangeDialog.setModal(true);//确保弹出的窗口在其他窗口前面
        selectTestRangeDialog.setVisible(true);
    }

    public void renderTableCheckBox(int fromRow, int toRow) {
        clearTableCheckBoxSelected();
        if ((toRow - fromRow) == (getRowCount() - 1)) {
            programmableSelectAll(true);
        }
        for (int i = fromRow - 1; i < toRow; i++) {
            setRowSelected(i, true);
        }
        repaint();
    }

    private void clearTableCheckBoxSelected() {
        for (int i = 0; i < getRowCount(); i++) {
            setRowSelected(i, false);
        }
    }

    //getTableList()排序后，重新插入数据库，进行更新
    public void saveOrderDataToDB() {
        List<ExcelRowDataModel> excelRowDataModelList = new ArrayList<>();
        int rowCount = getRowCount();
        for (int row = 0; row < rowCount; row++) {
            List<String> rowData = getRowData(row);
            ExcelRowDataModel excelRowDataModel = new ExcelRowDataModel();
            excelRowDataModel.setExcelFileName(CaseConfigJson.getInstance().getOriginalExcelCaseFilePath());
            excelRowDataModel.setSheetName(getSheetName());
//        excelRowDataModel.setData(excelRow.getData());
            excelRowDataModel.setData(rowData);
            excelRowDataModel.setRowNumber(row + 1);
            excelRowDataModelList.add(excelRowDataModel);
        }
        OperationTargetHolder.getExcelKit().updateOrderExcelCaseData(excelRowDataModelList);
    }


//    public void saveExcelCaseRowDataToDB(int row) {

    /// /        System.out.println("ColumnNameConstants.getInstance()--" + ColumnNameConstants.getInstance());
//        List<ExcelCaseModel> excelCaseModelList = new ArrayList<>();
//        int uuidColumnId = findTableColumnIndexByName(UUID_KEY);
//        if (uuidColumnId == -1)
//            return;
//        String uuid = (String) ObjectUtils.defaultIfNull(getModel().getValueAt(row, uuidColumnId), "");
//        JsonResponse<ExcelCaseModel> res = ClientManager.getExcelManager().findExcelCaseRow(uuid);
//        if (!res.isOk()) {
//            return;
//        } else {
//            ExcelCaseModel excelCaseModel = res.getData();
//            if (excelCaseModel != null) {
//                int testCaseID = findTableColumnIndexByName(ColumnNameConstants.getInstance().getTestCaseID());
//                if (testCaseID != -1) {
//                    excelCaseModel.setTestCaseID(ObjectUtils.defaultIfNull(getModel().getValueAt(row, testCaseID), "").toString());
//                }
//                int testKeyId = findTableColumnIndexByName(ColumnNameConstants.getInstance().getTestKey());
//                if (testKeyId != -1) {
//                    excelCaseModel.setTestKey(ObjectUtils.defaultIfNull(getModel().getValueAt(row, testKeyId), "").toString());
//                }
//                int initialConditionId = findTableColumnIndexByName(ColumnNameConstants.getInstance().getInitialCondition());
//                if (initialConditionId != -1) {
//                    excelCaseModel.setInitialCondition(ObjectUtils.defaultIfNull(getModel().getValueAt(row, initialConditionId), "").toString());
//                }
//                int actionId = findTableColumnIndexByName(ColumnNameConstants.getInstance().getAction());
//                if (actionId != -1) {
//                    excelCaseModel.setAction(ObjectUtils.defaultIfNull(getModel().getValueAt(row, actionId), "").toString());
//                }
//                int expectedResultId = findTableColumnIndexByName(ColumnNameConstants.getInstance().getExpectedResult());
//                if (expectedResultId != -1) {
//                    excelCaseModel.setExpectedResult(ObjectUtils.defaultIfNull(getModel().getValueAt(row, expectedResultId), "").toString());
//                }
//                int preconditionSequencesId = findTableColumnIndexByName(ColumnNameConstants.getInstance().getPreconditionSequences());
//                if (preconditionSequencesId != -1) {
//                    excelCaseModel.setInitTestSequences(ObjectUtils.defaultIfNull(getModel().getValueAt(row, preconditionSequencesId), "").toString());
//                }
//                int actionTestSequencesId = findTableColumnIndexByName(ColumnNameConstants.getInstance().getOperationStepSequences());
//                if (actionTestSequencesId != -1) {
//                    excelCaseModel.setActionTestSequences(ObjectUtils.defaultIfNull(getModel().getValueAt(row, actionTestSequencesId), "").toString());
//                }
//                int expectedResultSequencesId = findTableColumnIndexByName(ColumnNameConstants.getInstance().getExpectedResultSequences());
//                if (expectedResultSequencesId != -1) {
//                    excelCaseModel.setExpectedTestSequences(ObjectUtils.defaultIfNull(getModel().getValueAt(row, expectedResultSequencesId), "").toString());
//                }
//                int actualResultId = findTableColumnIndexByName(ColumnNameConstants.getInstance().getActualResult());
//                if (actualResultId != -1) {
//                    excelCaseModel.setActualResult(ObjectUtils.defaultIfNull(getModel().getValueAt(row, actualResultId), "").toString());
//                }
//                int testResultId = findTableColumnIndexByName(ColumnNameConstants.getInstance().getTestResult());
//                if (testResultId != -1) {
//                    excelCaseModel.setTestResult(ObjectUtils.defaultIfNull(getModel().getValueAt(row, testResultId), "").toString());
//                }
//                int testerId = findTableColumnIndexByName(ColumnNameConstants.getInstance().getTester());
//                if (testerId != -1) {
//                    excelCaseModel.setTester(ObjectUtils.defaultIfNull(getModel().getValueAt(row, testerId), "").toString());
//                }
//                int testTimeId = findTableColumnIndexByName(ColumnNameConstants.getInstance().getTestTime());
//                if (testTimeId != -1) {
//                    excelCaseModel.setTestTime(ObjectUtils.defaultIfNull(getModel().getValueAt(row, testTimeId), "").toString());
//                }
//                int remarkId = findTableColumnIndexByName(ColumnNameConstants.getInstance().getRemark());
//                if (remarkId != -1) {
//                    excelCaseModel.setRemark(ObjectUtils.defaultIfNull(getModel().getValueAt(row, remarkId), "").toString());
//                }
//                excelCaseModelList.add(excelCaseModel);
//            } else {
//                excelCaseModelList.add(getExcelCaseModel(row));
//            }
//        }
//        ClientManager.getExcelManager().updateExcelCase(excelCaseModelList);
//    }
    public void saveExcelCaseRowDataToDB(int row) {
        saveExcelCaseRowDataToDB(false, row);
    }


    public void saveExcelCaseRowDataToDB(boolean MultipleTest, int row) {
        List<String> rowData = getRowData(row);
        if (rowData == null) return;
        int selectedId;
        if (MultipleTest) {
            selectedId = findTableColumnIndexByName(ColumnNameConstants.getInstance().getChoose());
            if (selectedId != -1 && selectedId - 1 < rowData.size()) {
                rowData.set(selectedId - 1, "YES");
            }
        }
        ExcelRowDataModel excelRowDataModel = new ExcelRowDataModel();
        excelRowDataModel.setExcelFileName(CaseConfigJson.getInstance().getOriginalExcelCaseFilePath());
        excelRowDataModel.setSheetName(getSheetName());
        excelRowDataModel.setData(rowData);
        excelRowDataModel.setRowNumber(row + 1);
        log.info("保存到数据库，行：{}", excelRowDataModel.getRowNumber());
        OperationTargetHolder.getExcelKit().updateExcelCase(excelRowDataModel);
    }


//    public ExcelCaseModel getExcelCaseModel(int row) {
//        List<Object> rowDataList = new ArrayList<>();
//        rowDataList.add(0);  //id为Integer
//        rowDataList.addAll(getTableRowData(row));
//        ExcelCaseModel excelCaseModel = new ExcelCaseModel();
//        try {
//            listToModel(rowDataList, excelCaseModel);
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//        }
//        return excelCaseModel;
//    }

    private Object getColumnValue(int row, String columnName) {
        int columnIndex = SwingUtil.getColumnIndex(this, columnName);
        if (columnIndex == -1)
            return "";
        return ObjectUtils.defaultIfNull(getModel().getValueAt(row, columnIndex), "");
    }

    private Collection<?> getTableRowData(int row) {
        List<Object> dataList = new ArrayList<>();
        dataList.add(getColumnValue(row, UUID_KEY));
        dataList.add(getColumnValue(row, "tableName"));
        dataList.add(getColumnValue(row, ColumnNameConstants.getInstance().getTestCaseID()));
        dataList.add(getColumnValue(row, ColumnNameConstants.getInstance().getTestKey()));
        dataList.add(getColumnValue(row, ColumnNameConstants.getInstance().getInitialCondition()));
        dataList.add(getColumnValue(row, ColumnNameConstants.getInstance().getAction()));
        dataList.add(getColumnValue(row, ColumnNameConstants.getInstance().getExpectedResult()));
        dataList.add(getColumnValue(row, ColumnNameConstants.getInstance().getPreconditionSequences()));
        dataList.add(getColumnValue(row, ColumnNameConstants.getInstance().getOperationStepSequences()));
        dataList.add(getColumnValue(row, ColumnNameConstants.getInstance().getExpectedResultSequences()));
        dataList.add(getColumnValue(row, ColumnNameConstants.getInstance().getActualResult()));
        dataList.add(getColumnValue(row, ColumnNameConstants.getInstance().getTestResult()));
        dataList.add(getColumnValue(row, ColumnNameConstants.getInstance().getTester()));
        dataList.add(getColumnValue(row, ColumnNameConstants.getInstance().getTestTime()));
        dataList.add(getColumnValue(row, ColumnNameConstants.getInstance().getRemark()));
        dataList.add("" + getExcelRowExecuteTimes(row) * (Integer) excelCaseControlPanel.getTestCycleSpinner().getValue());
        dataList.add("0");
        dataList.add("0");
        return dataList;
    }

    public void updateExcelCaseTableData(int row, List<TestStep> testStepList) {
        String preconditionType = ColumnNameConstants.getInstance().getPreconditionSequences();
        String operationType = ColumnNameConstants.getInstance().getOperationStepSequences();
        String expectedResultType = ColumnNameConstants.getInstance().getExpectedResultSequences();
        int preconditionSequencesColumnId = findTableColumnIndexByName(preconditionType);
        int operationStepSequencesColumnId = findTableColumnIndexByName(operationType);
        int expectedResultSequencesColumnId = findTableColumnIndexByName(expectedResultType);
        List<TestStep> matchingPreconditionSteps = getTestStepsByType(testStepList, preconditionType);
        if (!matchingPreconditionSteps.isEmpty() && preconditionSequencesColumnId != -1) {
            setValueAt(generateCellContent(matchingPreconditionSteps), row, preconditionSequencesColumnId);
        }
        List<TestStep> matchingOperationSteps = getTestStepsByType(testStepList, operationType);
        if (!matchingOperationSteps.isEmpty() && operationStepSequencesColumnId != -1) {
            setValueAt(generateCellContent(matchingOperationSteps), row, operationStepSequencesColumnId);
        }
        List<TestStep> matchingExpectedResultSteps = getTestStepsByType(testStepList, expectedResultType);
        if (!matchingExpectedResultSteps.isEmpty() && expectedResultSequencesColumnId != -1) {
            setValueAt(generateCellContent(matchingExpectedResultSteps), row, expectedResultSequencesColumnId);
        }
        saveExcelCaseRowDataToDB(row);
    }

    @NotNull
    private static List<TestStep> getTestStepsByType(List<TestStep> testStepList, String testStepType) {
        return testStepList.stream()
                .filter(step -> testStepType.equals(step.getType()))
                .collect(Collectors.toList());
    }

    private String generateCellContent(List<TestStep> testStepList) {
        List<String> cellContentList = new ArrayList<>();
        for (TestStep testStep : testStepList) {
            cellContentList.add(testStep.getTestStep());
        }
        return cellContentWrap(cellContentList);
    }

    private String generateCellContentByType(List<TestStep> testStepList, String type) {
        List<String> cellContentList = new ArrayList<>();
        for (TestStep testStep : testStepList) {
            if (testStep.getType().equals(type)) {
                cellContentList.add(testStep.getTestStep());
            }
        }
        return cellContentWrap(cellContentList);
    }

    @Override
    public void updateCell(int editRow, int editColumn) {
        int row = editRow == -1 ? getSelectedRow() : editRow;
        int modelRow = convertRowIndexToModel(row);
        excelCaseTabPaneView.updateTitleCaseSaveStatus(false);
        saveExcelCaseRowDataToDB(modelRow);
        createTestStep(modelRow, editColumn);
        updateRowHeight(row);
        updateFrozenHeight(row);
    }

    public void updateFrozenHeight(int row) {
//        excelCaseRenderTabbedPane.setFrozenHeight(row, getRowHeight(row));
        int rowHeight = getRowHeight(row);
        int old = frozenColumnTable.getRowHeaderTable().getRowHeight(row);
        if (old != rowHeight) {
            frozenColumnTable.getRowHeaderTable().setRowHeight(row, rowHeight);
        }
    }

    @Override
    public void syncButtonStatus(Map<String, Boolean> map) {
        map.forEach((menuItemName, enabled) -> {
            JMenuItem menuItem = findMenuItemByName(menuItemName);
            if (menuItem == null) return;
            menuItem.setEnabled(enabled);
        });
    }


    /**
     * 同步表头设置
     *
     * @param headerConfigList 表头设置
     */
    public void syncTableCaseHeader(List<CaseHeaderContent> headerConfigList) {
        if (CollectionUtils.isNotEmpty(headerConfigList)) {
            for (CaseHeaderContent caseHeaderContent : headerConfigList) {
                int columnId = caseHeaderContent.getColumnIndex();
                getTableHeader().getColumnModel().getColumn(columnId)
                        .setPreferredWidth(caseHeaderContent.getColumnWidth());
                if (columnId != 0 && !caseHeaderContent.isBuiltin() && !caseHeaderContent.isVisible()) {
                    hiddenColumn(columnId);
                }
            }
        }
    }

    public int findTableColumnIndexByName(String columnName) {
        //log.info("寻找列名：{}的id", columnName);
        if (StringUtils.isEmpty(columnName)) return -1;
        try {
            return SwingUtil.getColumnIndex(this, columnName);
        } catch (Exception e) {
            log.error("{}用例表没有列名：{}", sheetName, columnName);
            return -1;
        }
    }

}
