package ui.layout.left.display.components.tappane.case_mgmt.excelcase;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Excel table thread manager
 * Manages thread pools for Excel table operations to avoid creating too many threads
 */
public class ExcelTableThreadManager {

    private static final ExcelTableThreadManager INSTANCE = new ExcelTableThreadManager();

    // Background processing thread pool
    private final ExecutorService backgroundExecutor;

    // Row height calculation thread pool
    private final ExecutorService rowHeightExecutor;

    private ExcelTableThreadManager() {
        // Create background processing thread pool
        this.backgroundExecutor = Executors.newFixedThreadPool(2, new ThreadFactory() {
            private final AtomicInteger threadNumber = new AtomicInteger(1);

            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r, "ExcelTable-Background-" + threadNumber.getAndIncrement());
                t.setDaemon(true);
                t.setPriority(Thread.NORM_PRIORITY - 1); // Lower priority
                return t;
            }
        });

        // Create row height calculation thread pool
        this.rowHeightExecutor = Executors.newSingleThreadExecutor(new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r, "ExcelTable-RowHeight");
                t.setDaemon(true);
                t.setPriority(Thread.NORM_PRIORITY - 2); // Even lower priority
                return t;
            }
        });

        System.out.println("Excel table thread manager initialized");
    }

    public static ExcelTableThreadManager getInstance() {
        return INSTANCE;
    }

    /**
     * Get background processing executor
     */
    public ExecutorService getBackgroundExecutor() {
        return backgroundExecutor;
    }

    /**
     * Get row height calculation executor
     */
    public ExecutorService getRowHeightExecutor() {
        return rowHeightExecutor;
    }

    /**
     * Shutdown all thread pools
     */
    public void shutdown() {
        System.out.println("Shutting down Excel table thread pools");

        backgroundExecutor.shutdown();
        rowHeightExecutor.shutdown();

        try {
            if (!backgroundExecutor.awaitTermination(5, java.util.concurrent.TimeUnit.SECONDS)) {
                backgroundExecutor.shutdownNow();
            }
            if (!rowHeightExecutor.awaitTermination(5, java.util.concurrent.TimeUnit.SECONDS)) {
                rowHeightExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            backgroundExecutor.shutdownNow();
            rowHeightExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }

        System.out.println("Excel table thread pools shutdown completed");
    }

    /**
     * Get thread pool status
     */
    public String getStatus() {
        return String.format("Background pool: %s, RowHeight pool: %s",
                backgroundExecutor.isShutdown() ? "SHUTDOWN" : "RUNNING",
                rowHeightExecutor.isShutdown() ? "SHUTDOWN" : "RUNNING");
    }
}
