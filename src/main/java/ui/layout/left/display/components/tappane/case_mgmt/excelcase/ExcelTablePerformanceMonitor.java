package ui.layout.left.display.components.tappane.case_mgmt.excelcase;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Excel表格性能监控工具
 * 用于监控和统计表格渲染性能指标
 */
@Slf4j
public class ExcelTablePerformanceMonitor {
    
    private static final ExcelTablePerformanceMonitor INSTANCE = new ExcelTablePerformanceMonitor();
    
    // 性能计数器
    private final ConcurrentHashMap<String, AtomicLong> counters = new ConcurrentHashMap<>();
    
    // 时间统计
    private final ConcurrentHashMap<String, Long> timings = new ConcurrentHashMap<>();
    
    private ExcelTablePerformanceMonitor() {}
    
    public static ExcelTablePerformanceMonitor getInstance() {
        return INSTANCE;
    }
    
    /**
     * 开始计时
     */
    public void startTiming(String operation) {
        timings.put(operation, System.currentTimeMillis());
    }
    
    /**
     * 结束计时并记录
     */
    public long endTiming(String operation) {
        Long startTime = timings.remove(operation);
        if (startTime != null) {
            long duration = System.currentTimeMillis() - startTime;
            log.info("操作 [{}] 耗时: {} ms", operation, duration);
            return duration;
        }
        return 0;
    }
    
    /**
     * 增加计数器
     */
    public void incrementCounter(String counterName) {
        counters.computeIfAbsent(counterName, k -> new AtomicLong(0)).incrementAndGet();
    }
    
    /**
     * 增加计数器指定值
     */
    public void addToCounter(String counterName, long value) {
        counters.computeIfAbsent(counterName, k -> new AtomicLong(0)).addAndGet(value);
    }
    
    /**
     * 获取计数器值
     */
    public long getCounter(String counterName) {
        AtomicLong counter = counters.get(counterName);
        return counter != null ? counter.get() : 0;
    }
    
    /**
     * 重置计数器
     */
    public void resetCounter(String counterName) {
        counters.remove(counterName);
    }
    
    /**
     * 重置所有计数器
     */
    public void resetAllCounters() {
        counters.clear();
        timings.clear();
    }
    
    /**
     * 记录表格渲染开始
     */
    public void recordRenderStart(String tableName, int rowCount) {
        String operation = "render_" + tableName;
        startTiming(operation);
        addToCounter("total_rows_rendered", rowCount);
        incrementCounter("render_operations");
        log.info("开始渲染表格 [{}]，行数: {}", tableName, rowCount);
    }
    
    /**
     * 记录表格渲染完成
     */
    public void recordRenderComplete(String tableName) {
        String operation = "render_" + tableName;
        long duration = endTiming(operation);
        incrementCounter("render_completed");
        
        // 计算渲染速度
        long totalRows = getCounter("total_rows_rendered");
        if (duration > 0 && totalRows > 0) {
            long rowsPerSecond = (totalRows * 1000) / duration;
            log.info("表格 [{}] 渲染完成，速度: {} 行/秒", tableName, rowsPerSecond);
        }
    }
    
    /**
     * 记录批处理信息
     */
    public void recordBatchProcessed(int batchSize, long batchDuration) {
        addToCounter("total_batches", 1);
        addToCounter("total_batch_rows", batchSize);
        addToCounter("total_batch_time", batchDuration);
        
        long avgBatchTime = getCounter("total_batch_time") / getCounter("total_batches");
        log.debug("批处理完成，大小: {}，耗时: {} ms，平均耗时: {} ms", 
                batchSize, batchDuration, avgBatchTime);
    }
    
    /**
     * 记录行高计算
     */
    public void recordRowHeightCalculation(boolean cacheHit) {
        if (cacheHit) {
            incrementCounter("row_height_cache_hits");
        } else {
            incrementCounter("row_height_cache_misses");
        }
    }
    
    /**
     * 记录内存使用
     */
    public void recordMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        log.debug("内存使用情况 - 总内存: {} MB, 已用: {} MB, 空闲: {} MB", 
                totalMemory / (1024 * 1024), 
                usedMemory / (1024 * 1024), 
                freeMemory / (1024 * 1024));
    }
    
    /**
     * 获取性能统计报告
     */
    public String getPerformanceReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== Excel表格性能统计报告 ===\n");
        
        // 渲染统计
        long renderOps = getCounter("render_operations");
        long renderCompleted = getCounter("render_completed");
        long totalRows = getCounter("total_rows_rendered");
        
        report.append(String.format("渲染操作: %d 次，完成: %d 次，总行数: %d\n", 
                renderOps, renderCompleted, totalRows));
        
        // 批处理统计
        long totalBatches = getCounter("total_batches");
        long totalBatchRows = getCounter("total_batch_rows");
        long totalBatchTime = getCounter("total_batch_time");
        
        if (totalBatches > 0) {
            long avgBatchSize = totalBatchRows / totalBatches;
            long avgBatchTime = totalBatchTime / totalBatches;
            report.append(String.format("批处理: %d 批，平均大小: %d 行，平均耗时: %d ms\n", 
                    totalBatches, avgBatchSize, avgBatchTime));
        }
        
        // 缓存统计
        long cacheHits = getCounter("row_height_cache_hits");
        long cacheMisses = getCounter("row_height_cache_misses");
        long totalCacheAccess = cacheHits + cacheMisses;
        
        if (totalCacheAccess > 0) {
            double hitRate = (double) cacheHits / totalCacheAccess * 100;
            report.append(String.format("行高缓存: 命中 %d 次，未命中 %d 次，命中率: %.2f%%\n", 
                    cacheHits, cacheMisses, hitRate));
        }
        
        // 内存统计
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = (runtime.totalMemory() - runtime.freeMemory()) / (1024 * 1024);
        report.append(String.format("当前内存使用: %d MB\n", usedMemory));
        
        return report.toString();
    }
    
    /**
     * 打印性能报告到日志
     */
    public void logPerformanceReport() {
        log.info("\n{}", getPerformanceReport());
    }
}
