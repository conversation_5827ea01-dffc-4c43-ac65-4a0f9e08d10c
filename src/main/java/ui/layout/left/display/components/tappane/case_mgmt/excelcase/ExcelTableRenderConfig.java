package ui.layout.left.display.components.tappane.case_mgmt.excelcase;

/**
 * Excel table rendering configuration class
 * Used to manage performance parameters for table rendering
 */
public class ExcelTableRenderConfig {

    /**
     * Default batch size
     */
    public static final int DEFAULT_BATCH_SIZE = 500;

    /**
     * Minimum batch size
     */
    public static final int MIN_BATCH_SIZE = 100;

    /**
     * Maximum batch size
     */
    public static final int MAX_BATCH_SIZE = 2000;

    /**
     * Batch processing interval in milliseconds
     */
    public static final int BATCH_INTERVAL_MS = 10;

    /**
     * Scroll listener delay time in milliseconds
     */
    public static final int SCROLL_LISTENER_DELAY_MS = 100;

    /**
     * Visible area extension rows (extend up and down)
     */
    public static final int VISIBLE_AREA_EXTENSION = 10;

    /**
     * Maximum capacity of row height cache
     */
    public static final int ROW_HEIGHT_CACHE_MAX_SIZE = 10000;

    /**
     * Calculate optimal batch size based on data volume
     *
     * @param totalRows total number of rows
     * @return optimized batch size
     */
    public static int calculateOptimalBatchSize(int totalRows) {
        if (totalRows <= 1000) {
            return MIN_BATCH_SIZE;
        } else if (totalRows <= 5000) {
            return DEFAULT_BATCH_SIZE;
        } else if (totalRows <= 20000) {
            return 1000;
        } else {
            return MAX_BATCH_SIZE;
        }
    }

    /**
     * Dynamically adjust batch interval based on system performance
     *
     * @param availableMemory available memory in MB
     * @return adjusted interval time
     */
    public static int calculateBatchInterval(long availableMemory) {
        if (availableMemory > 1024) { // Greater than 1GB
            return 5;
        } else if (availableMemory > 512) { // Greater than 512MB
            return BATCH_INTERVAL_MS;
        } else {
            return 20; // Increase interval when memory is low
        }
    }
}
